{"singleQuote": true, "trailingComma": "all", "printWidth": 100, "tabWidth": 2, "semi": true, "useTabs": false, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "overrides": [{"files": "*.js", "options": {"parser": "babel"}}], "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^react$", "^react-native$", "^expo$", "^expo", "^@?react-native", "^@?react-navigation", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}