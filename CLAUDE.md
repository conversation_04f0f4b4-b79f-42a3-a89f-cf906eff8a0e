# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npx expo start` - Start development server with Metro bundler
- `npx expo run:ios` - Build and run iOS app in simulator
- `npx expo run:android` - Build and run Android app in emulator/device
- `npx expo start --web` - Run web version in browser

### Development Workflow
- `APP_VARIANT=development npx expo run` - Run with development variant
- `node ./scripts/reset-project.js` - Reset project to clean state

### Testing & Code Quality
- `jest --watchAll` - Run tests in watch mode
- `prettier --write src/**/*.{js,jsx,ts,tsx}` - Format code
- `prettier --check src/**/*.{js,jsx,ts,tsx}` - Check formatting
- No dedicated lint command - relies on TypeScript compiler and Prettier

### Build Commands
- `bun expo prebuild --clear` - Generate native projects from scratch
- `bun install` - Install dependencies (using Bun package manager)
- `npx @react-native-community/cli clean` - Clean Metro and React Native caches

## Architecture Overview

### Technology Stack
- **Framework**: React Native 0.79.5 + Expo SDK 53 + React 19
- **Navigation**: Expo Router 5.1.4 (file-based routing)
- **State Management**: Jotai 2.10.3 + TanStack Query 5.61.4
- **Authentication**: Kinde React Native SDK 2.0.0
- **Backend**: ✅ **Go Backend API v1** (migrated from Directus SDK)
- **Storage**: MMKV 3.1.0 + Expo Secure Store
- **Styling**: React Native Unistyles 2.12.0
- **Type Safety**: TypeScript 5.8.3 + Valibot 1.1.0

### Feature-First Architecture

The codebase follows a feature-first architecture with the Gospel feature serving as the production-ready template:

```
/src/features/[feature-name]/
├── api/
│   ├── directus.ts      # Directus SDK integration
│   └── index.ts         # API exports
├── types/
│   ├── directus.ts      # Zod schemas & TypeScript types
│   └── index.ts         # Type exports
├── constants/
│   └── [feature]Keys.ts # Static mappings for performance
├── components/          # Feature-specific components
├── screens/             # Feature screens
├── hooks/               # Feature hooks
└── index.ts            # Feature exports
```

### Key Features & Migration Status

**✅ Gospel Feature (TEMPLATE)**: **COMPLETE OFFLINE MINISTRY SOLUTION**
- Location: `src/features/gospel/`
- **NEW Architecture**: Direct Go backend integration (`/api/v1/guide/*`)
- **Migration**: Directus SDK → Clean API calls with enhanced TanStack Query hooks
- **API**: `useGospelSections()`, `useGospelSection()`, `useGospelStep()`, `usePrefetchGospelContent()`
- **Performance**: Direct API calls + Redis caching (15min TTL) + **INFINITE MMKV persistence**
- **Complete Offline**: Background prefetch of all Gospel content for instant offline access
- **Smart Caching**: TanStack Query deduplication + infinite MMKV persistence
- **Documentation**: `docs/MIGRATION_SUMMARY.md` + `docs/mmkv_persistence_implementation_plan.md` + `docs/prefetch_gospel_implementation_guide.md`
- **Status**: ✅ Production-ready with complete offline ministry functionality

**✅ Prospects Feature (TEMPLATE)**: **COMPLETE AUTHENTICATED MINISTRY SOLUTION**
- Location: `src/features/prospects/`
- **NEW Architecture**: Direct Go backend integration (`/api/v1/prospects/*`) with Kinde JWT authentication
- **Migration**: Mock data → Clean API calls with validated TanStack Query hooks + Valibot runtime validation
- **API**: `useProspects()`, `useProspectDetail()`, `useCreateProspect()`, `useUpdateProspect()`, `useDeleteProspect()`, `useDashboard()`
- **Authentication**: Kinde OAuth 2.0 + PKCE with secure token storage (Expo SecureStore + custom chunking)
- **Performance**: Direct API calls + Redis caching (15min TTL) + 24hr MMKV persistence + optimistic updates
- **User-Scoped Data**: JWT-authenticated endpoints with automatic user isolation
- **Enhanced Data Model**: PersonName structure, unified contactInfo, status progression (PROSPECT→SAVED→BAPTIZED→FOUNDATION→MINISTRY)
- **Theme Integration**: All colors use semantic Unistyles tokens for light/dark mode support
- **Status**: ✅ Production-ready with complete authenticated prospect management

**⏳ Ready for Go Backend Migration** (using Gospel + Prospects templates):
- **Users**: Profile management (Priority 1) - Use Prospects auth pattern
- **Devotional**: Daily content (Priority 2) - Use Gospel public pattern  
- **Lessons**: Educational materials (Priority 3) - Use Gospel public pattern

### State Management

**Authentication**: Jotai atoms with MMKV persistence
- Location: `src/state/atoms/auth.ts`
- Provider: Kinde Auth with OAuth flow
- Storage: Expo Secure Store for sensitive data

**Data Caching**: TanStack Query with MMKV persistence + Jotai integration
- **Gospel**: `useValidatedGospelQuery` with infinite MMKV persistence for offline ministry
- **Other Features**: Standard `atomWithQuery` patterns with local-first approach
- **Storage**: Custom MMKV persister (`src/api/client/persistence.ts`) for TanStack Query cache

### API Architecture

**✅ NEW: Go Backend API Pattern** (Gospel feature template):
```typescript
// Enhanced API client with axios + interceptors + retry logic
import { api } from '@/api/client';
import { SectionsListResponse } from '@/api/types/guides';

export const getSections = async () => {
  return api.get<SectionsListResponse>('/api/v1/guide/sections');
};
```

**Enhanced TanStack Query Hooks with MMKV Persistence**:
```typescript
// Gospel hooks with infinite MMKV persistence for offline ministry
export const useGospelSections = () => {
  return useValidatedGospelQuery(
    [CACHE_KEYS.guides.sections],
    () => guides.getSections(),
    SectionsListResponseSchema,
    {
      select: (response) => response.sections,
    }
  );
};
```

**Type Safety Pattern**:
```typescript
// Clean interfaces matching Go backend DTOs exactly
export interface SectionSummary {
  title: string;
  sectionNo: number; 
  subtitle: string;
  image?: Image;
  hint: string;
  overview: string;
}
```

### File Structure Conventions

**Navigation**: Expo Router file-based routing
- `/app/` - Screen components and layouts
- `/app/(tabs)/` - Tab navigation screens
- `/app/(modals)/` - Modal screens

**Components**: Organized by purpose
- `/components/ui/` - Basic UI elements (Button, ThemedText)
- `/components/layout/` - Layout components (ParallaxScrollView)
- `/components/form/` - Form-specific components

**Styling**: Unistyles theme system
- Location: `src/libs/unistyles/`
- Breakpoints, colors, typography tokens defined
- Theme-aware components with light/dark mode support

## Development Guidelines

### Adding New Features
1. Use Gospel feature as exact template (`src/features/gospel/`)
2. Create Go backend API endpoints (`/api/v1/feature/*`)  
3. Create Valibot schemas with runtime validation (`/src/api/schemas/feature.ts`)
4. Use `useValidatedQuery` hooks for type-safe API calls with validation
5. Add feature-specific cache keys aligned with backend Redis keys

### Migration Pattern (API-First → Go Backend)
**✅ COMPLETED TEMPLATE**: Gospel feature migration from Directus SDK to Go backend
When migrating features to Go backend (follow Gospel example):

1. **API Infrastructure**: Use established `/src/api/` structure
   - **Client**: Enhanced axios with interceptors, retry, error handling  
   - **Schemas**: Valibot schemas for runtime validation and type inference
   - **Endpoints**: Organized API calls (`/api/v1/feature/*`)
   - **Hooks**: Enhanced `useValidatedGospelQuery` with infinite MMKV persistence for Gospel content

2. **Feature Migration**: 
   - **New Hooks**: Replace atoms with `useValidatedQuery()` API hooks
   - **Screen Updates**: Update to use validated hooks (`useGospelSections` example)
   - **Schema-First**: Use Valibot schemas as single source of truth for types
   - **Cleanup**: Remove old type files, consolidate to schemas

3. **Environment**: Update to point to Go backend (`/api/v1`)

**Reference**: `docs/MIGRATION_SUMMARY.md` for complete Gospel migration details

### Import Patterns
```tsx
// NEW: Validated API hooks with infinite MMKV persistence (Gospel template)
import { useGospelSections, useGospelSection, useGospelStep } from '@/features/gospel/hooks';

// Valibot schemas and inferred types (single source of truth)
import { type SectionSummary, SectionSummarySchema } from '@/api/schemas/guides';

// Enhanced API client with validation + persistence
import { api } from '@/api';
import { useValidatedGospelQuery } from '@/api/hooks/useValidatedQuery';

// Legacy (deprecated - use schemas instead)
// import { SectionSummary } from '@/api/types/guides';
// import { useApiQuery } from '@/api/hooks';

// Shared components
import { Button } from '@/components/ui';
import { ParallaxScrollView } from '@/components/layout';

// Hooks and utilities
import { useAuth } from '@/hooks/auth';
import { useThemeColor } from '@/hooks/theme';
```

### Git Commit Guidelines
**IMPORTANT**: Follow these commit message rules:
- **NO Co-authored-by lines** - Do not include AI attribution in commits
- **NO AI generation mentions** - Do not mention Claude Code or AI assistance
- **Concise messages** - Keep commit titles short and descriptive
- **Focus on impact** - Describe what changed and why, not how it was made

**Example Good Commit**:
```bash
git commit -m "Fix Gospel section loading issue

- Update API response handling in section queries
- Remove legacy type compatibility layer  
- Clean up unused endpoint files"
```

**Example Bad Commit** (❌ Do not use):
```bash
git commit -m "Fix issues

🤖 Generated with Claude Code
Co-Authored-By: Claude <<EMAIL>>"
```

### Testing
- Framework: Jest with Expo preset
- Location: Components have `__tests__` folders
- Run: `jest --watchAll` for development
- Snapshots: Automatically generated for component tests

## Key Implementation Notes

### Performance Optimizations
- **Enhanced API Client**: Axios with interceptors, retry logic, and error handling
- **Smart Caching**: TanStack Query + Redis backend caching (15min TTL)
- **Infinite Gospel Persistence**: MMKV storage for Gospel content with `gcTime: Infinity`
- **Offline-First**: Custom MMKV persister for TanStack Query with automatic background refresh
- **Type-Safe Transforms**: Direct data selection with TypeScript generics in select functions

### Authentication Flow
- Kinde OAuth with PKCE for security
- Token storage in Expo Secure Store
- Profile sync with Jotai atoms
- Automatic token refresh handling

### Environment Configuration
- API URLs via `EXPO_PUBLIC_API_URL`
- Kinde config via `expo-constants`
- Platform-specific handling for iOS/Android/web

### Migration Progress - API-FIRST ARCHITECTURE ✅

- **✅ Gospel Feature**: **COMPLETE OFFLINE MINISTRY SOLUTION** (`/api/v1/guide/*`)
  - Template established for all other features
  - Direct API integration with enhanced performance
  - TanStack Query + infinite MMKV persistence for offline ministry work
  - Background prefetch system for complete offline access (`usePrefetchGospelContent`)
  - Custom MMKV persister (`src/api/client/persistence.ts`) with automatic background refresh
  - Smart caching with TanStack Query deduplication
  - **Documentation**: Complete implementation details in `docs/MIGRATION_SUMMARY.md` + `docs/mmkv_persistence_implementation_plan.md` + `docs/prefetch_gospel_implementation_guide.md`

- **✅ Prospects Feature**: **COMPLETE AUTHENTICATED MINISTRY SOLUTION** (`/api/v1/prospects/*`)
  - **Kinde JWT Authentication**: OAuth 2.0 + PKCE with secure token storage (Expo SecureStore + chunking)
  - **Runtime Validation**: Valibot schemas with comprehensive error handling and type inference
  - **Enhanced Data Model**: PersonName structure, unified contactInfo, status progression validation
  - **User-Scoped Security**: JWT-authenticated endpoints with automatic user isolation
  - **Optimistic Updates**: TanStack Query mutations with rollback on failure + 24hr MMKV persistence
  - **Theme Integration**: Semantic Unistyles color tokens for consistent light/dark mode support
  - **Clean Architecture**: Removed all legacy Directus code, follows Gospel API pattern exactly

- **⏳ Ready for Go Backend Migration** (using Gospel + Prospects templates):
  - **Users Feature**: Apply Prospects authentication pattern (Priority 1)
  - **Devotional Feature**: Apply Gospel public pattern (Priority 2)
  - **Lessons Feature**: Apply Gospel public pattern (Priority 3)

**🎉 DUAL TEMPLATE ARCHITECTURE**: Gospel (public/offline) + Prospects (authenticated/user-scoped) templates provide complete patterns for all future features. Mobile-backend integration now supports both public content and secure user data with consistent API architecture (`/api/v1/*`).

## Next Steps & Improvements

### ✅ Priority 1: Runtime API Validation (COMPLETED)
**Issue**: Property mismatches from Go backend migration cause silent failures (images not loading, data missing)
**✅ Solution**: Migrated to Valibot for runtime validation  
- ✅ Created comprehensive Valibot schemas in `/src/api/schemas/`
- ✅ Enhanced `useValidatedQuery` hooks with runtime validation
- ✅ Immediate error detection with detailed logging for API contract mismatches
- ✅ **Performance gains**: ~60% smaller bundle, 2-10x faster validation vs Zod
- ✅ **Success**: Caught and fixed Card component property mismatch (`referenceText` → `reference`)

### ✅ Priority 2: TypeScript Errors Resolution (COMPLETED)  
**Status**: ✅ **ALL ERRORS RESOLVED** - Reduced from 89+ to 0 TypeScript errors
**Fixed Issues**:
- ✅ Fixed TanStack Query v5 compatibility (deprecated `onError`, `select` function types)
- ✅ Fixed Gospel hooks data structure alignment with Go backend responses
- ✅ Updated component type annotations (FormInput, ProspectCard, GuideSection)
- ✅ Resolved duplicate export ambiguities and theme property access
- ✅ Migrated from duplicate type definitions to single-source-of-truth schemas
- ✅ Fixed auth hook placeholder for missing user API endpoints

### ✅ Priority 2.5: Migration Cleanup (COMPLETED)
**Status**: ✅ **ALL LEGACY CODE REMOVED** - Gospel feature migration fully complete
**Completed Cleanup**:
- ✅ Removed legacy compatibility types (`GuideSection`, `GuideStep`, `GuideBibleReference`)
- ✅ Fixed deprecated `substr()` → `slice()` in API client request ID generation
- ✅ Cleaned up unused types file `/api/endpoints/guides/types.ts`
- ✅ Updated imports to use direct schema imports from `/api/schemas/guides`
- ✅ **Result**: Zero TypeScript errors, no legacy code remaining

### ✅ Priority 3: MMKV Persistence Implementation (COMPLETED)
**Status**: ✅ **INFINITE GOSPEL PERSISTENCE IMPLEMENTED** - Offline ministry support complete
**Implemented Features**:
- ✅ Custom MMKV persister for TanStack Query (`src/api/client/persistence.ts`)
- ✅ `useValidatedGospelQuery` hook with `gcTime: Infinity` for infinite persistence
- ✅ Gospel hooks migrated to infinite persistence (`useGospelSections`, `useGospelSection`, `useGospelStep`)
- ✅ `PersistQueryClientProvider` configuration with offline-first settings
- ✅ Automatic background refresh when back online (`refetchOnReconnect: true`)
- ✅ Cache management utilities for debugging
- ✅ **Result**: Gospel content available indefinitely for offline ministry work

### ✅ Priority 4: Complete Offline Gospel Access (COMPLETED)
**Status**: ✅ **BACKGROUND PREFETCH SYSTEM IMPLEMENTED** - Complete offline ministry functionality
**Implemented Features**:
- ✅ `usePrefetchGospelContent` hook for silent background content download
- ✅ Sequential prefetch: sections first, then all steps within sections
- ✅ Same validation pattern as Gospel hooks to ensure cached data integrity
- ✅ Smart caching with TanStack Query deduplication (no duplicate fetches)
- ✅ Integrated into GospelScreen with zero UI impact
- ✅ Enhanced error handling and logging for debugging
- ✅ **Result**: Complete Gospel content available offline after first app usage

### ⏳ Priority 5: Feature Migration Pipeline (PENDING GO BACKEND ENDPOINTS)
**Template Established**: Gospel feature with Valibot validation + infinite MMKV persistence + prefetch system ready for replication
- **Users Feature**: Apply Gospel pattern with Valibot schemas (awaiting Go backend endpoints)  
- **Devotional Feature**: Daily content with runtime validation (awaiting Go backend endpoints)
- **Lessons Feature**: Educational materials with runtime validation (awaiting Go backend endpoints)
- **Prospects Feature**: Apply Valibot schemas when Go backend endpoints available

## Known Issues & Fixes

### React Native Unistyles Bundling Error
**Issue**: `Unable to resolve "react-native-unistyles/components/native/View"` during bundling with `react-native-reanimated@3.17.4`

**Root Cause**: `react-native-reanimated` attempts to import a non-existent path from `react-native-unistyles`. This appears to be a phantom import that was never properly implemented.

**Fix**: Added Metro resolver alias in `metro.config.js`:
```javascript
resolver.alias = {
  ...resolver.alias,
  'react-native-unistyles/components/native/View': require.resolve(
    'react-native/Libraries/Components/View/View.js'
  ),
};
```

**Prevention**: Ensure `react-native-unistyles` version stays at `2.12.0` as specified in `package.json` to maintain compatibility with the current `react-native-reanimated` version. But with Metro resolver fix, this issue should be resolved.

### API Response Handling Fix
**Issue**: Steps not loading in SectionDetail screen due to API response structure mismatch.

**Root Cause**: API query functions were returning full axios response objects, but validation hooks expected direct data after the migration to Valibot.

**Fix Applied**:
1. Updated all query functions in `/src/api/endpoints/guides/queries.ts` to return `response.data` instead of `response`
2. Updated validation hooks in `/src/api/hooks/useValidatedQuery.ts` to handle direct data instead of axios response objects
3. Fixed type signatures and validation flow for consistent data handling

**Result**: Section details now load correctly with steps displaying properly in the SectionDetail screen.