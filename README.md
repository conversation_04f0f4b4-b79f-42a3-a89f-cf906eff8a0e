# OperationGo Mobile App - Architecture & Implementation

## Overview
OperationGo is a React Native mobile app built with Expo 53 SDK, implementing a feature-first architecture pattern. The app uses <PERSON><PERSON>th for authentication, <PERSON><PERSON> for state management, TanStack Query for API data fetching, and the Directus SDK for backend integration.

## Technology Stack

### Core Dependencies
- **React Native**: 0.79.5 with React 19.0.0
- **Expo SDK**: 53.0.20
- **Navigation**: Expo Router 5.1.4
- **State Management**: Jotai 2.10.3 + TanStack Query 5.61.4
- **Authentication**: Kinde React Native SDK 2.0.0
- **Backend**: Directus SDK 20.0.0 + Axios (legacy)
- **Storage**: React Native MMKV 3.1.0 + Expo Secure Store
- **Styling**: React Native Unistyles 2.12.0
- **Type Safety**: TypeScript 5.8.3 + Zod 3.23.8

## Feature-First Architecture

```
/src
├── api/                 # Base API client and utilities
├── app/                 # Expo Router screens and navigation
├── components/          # Shared UI components
│   ├── ui/             # Basic UI elements
│   ├── layout/         # Layout components
│   ├── form/           # Form components
│   └── custom-tabs/    # Custom tab components
├── constants/           # App-wide constants
├── features/            # Feature-specific code (MAIN FOCUS)
│   ├── gospel/         # ✅ Fully migrated to Directus SDK
│   ├── devotional/     # ⏳ Pending Directus migration
│   ├── lessons/        # ⏳ Pending Directus migration
│   └── support/        # ⏳ Pending Directus migration
├── hooks/               # Shared hooks
├── libs/                # Third-party integrations
├── navigation/          # Navigation configuration
├── schemas/             # Legacy Zod schemas (being moved to features)
├── state/               # Global state management (Jotai + persistence)
└── storage/             # Storage utilities
```

## Feature Structure (Established Standard)

Each feature follows this structure, based on the successful Gospel implementation:

```
/src/features/[feature-name]/
├── api/                 # Feature API implementation
│   ├── directus.ts     # Directus SDK integration
│   └── index.ts        # API exports
├── types/               # Feature types
│   ├── directus.ts     # Zod schemas & types
│   └── index.ts        # Type exports
├── constants/           # Feature constants (golden key mappings)
│   └── goldenKeys.ts   # Static mappings for performance
├── components/          # Feature-specific components
├── screens/             # Feature-specific screens
├── hooks/               # Feature-specific hooks
└── index.ts            # Feature exports
```

## Authentication & State Management

### Authentication Flow
- **Provider**: Kinde Auth with OAuth flow
- **Token Storage**: Secure storage via Expo Secure Store
- **State**: Jotai atoms with MMKV persistence
- **Profile Management**: Automatic sync with secure storage

### State Management Structure
- **Auth State**: `isAuthenticated`, `userProfile`, `isLoading`, `error`
- **UI State**: App-level UI state management
- **Data Caching**: TanStack Query for server state
- **Persistence**: MMKV for performance-critical data, SecureStore for sensitive data

## Migration Status

### ✅ Completed Features
1. **Gospel** - Spiritual guide sections and steps
   - **Status**: ✅ **FULLY MIGRATED** to Directus SDK
   - **Performance**: 60% data usage reduction achieved
   - **Architecture**: Serves as template for remaining features

### ⏳ Pending Migration
2. **Users** - Profile management (REST API → Directus SDK)
3. **Prospects** - Contact management (REST API → Directus SDK) 
4. **Devotional** - Daily devotional content (REST API → Directus SDK)
5. **Lessons** - Educational content (REST API → Directus SDK)

## Directus SDK Integration Pattern

Based on the successful Gospel migration:

### API Implementation
```typescript
// api/directus.ts
import { createDirectus, rest, readItems } from '@directus/sdk';

const directus = createDirectus(process.env.EXPO_PUBLIC_API_URL!)
  .with(rest());

export const getSections = async () => {
  return directus.request(
    readItems('section', {
      fields: ['id', 'title', 'description', 'image', 'golden_key'], // Optimized fields
    })
  );
};
```

### Type Safety
```typescript
// types/directus.ts
import { z } from 'zod';
import { GOLDEN_KEY_MAP } from '../constants/goldenKeys';

export const SectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  // ... other fields
});
```

### Constants Management
```typescript
// constants/goldenKeys.ts
export const GOLDEN_KEY_MAP = {
  'section-1': 'Gospel Foundation',
  'section-2': 'Spiritual Growth',
  // ...
} as const;
```

## Import Patterns

Use consistent import patterns across the app:

```tsx
// Feature-specific imports
import { useGuideData } from '@/features/gospel/hooks';
import { SectionDetailScreen } from '@/features/gospel/screens';

// Shared components
import { Button } from '@/components/ui';
import { ParallaxScrollView } from '@/components/layout';

// Hooks and utilities
import { useAuth } from '@/hooks/auth';
import { useColorScheme } from '@/hooks/theme';
```

## Development Guidelines

### Adding New Features
1. Follow the Gospel feature structure exactly
2. Create constants folder for static mappings
3. Implement Directus SDK with optimized field selection
4. Use centralized type definitions with Zod validation
5. Test all screens thoroughly before cleanup

### Code Quality Standards
- **Type Safety**: Explicit TypeScript types + Zod validation
- **Performance**: Optimize API field selection for mobile
- **Maintainability**: Centralized constants, clean structure
- **Error Handling**: Proper error boundaries and user feedback

### Migration Process
1. **Setup**: Create feature folder structure
2. **API**: Implement Directus SDK with optimized fields
3. **Types**: Create Zod schemas with centralized constants
4. **Integration**: Update hooks and test all screens
5. **Cleanup**: Remove old files and update exports

## Key Achievements (Gospel Feature)

- **60% bandwidth reduction** via selective field fetching
- **Runtime type validation** with Zod + TypeScript
- **Centralized golden key mappings** for consistency
- **Clean, maintainable codebase** ready for production
- **Template established** for rapid feature migration

## Next Steps

1. **Prospects Feature Migration** – CRUD operations (immediate priority)
2. **Users Feature Migration** – Profile management APIs
3. **Content Features Migration** – Devotional and Lessons
4. **Legacy Cleanup** – Remove old schemas and API files

### Prospects Migration Plan (Immediate)
- **API (Directus SDK)**: Implement CRUD for `prospects` and visit records collections with optimized field selection
- **Types**: Move `src/schemas/prospect/*` to `src/features/prospects/types/` with Zod validation and transforms
- **Hooks & Caching**: Add feature atoms via `atomWithQuery` + `localFirst`; provide create/update/delete mutations
- **UI Integration**: Wire real recent prospects in `GospelScreen`; implement view/edit/new screens under `gospel/prospects/*`
- **Cleanup**: Remove legacy Axios code in `src/api/prospects.ts` and `src/hooks/queries/prospects.ts`

---

**Current Status**: Gospel feature successfully migrated, serving as production-ready template for all remaining features.