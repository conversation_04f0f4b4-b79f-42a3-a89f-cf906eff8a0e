const IS_DEV = process.env.APP_VARIANT === 'development';
const IS_PREVIEW = process.env.APP_VARIANT === 'preview';

const getUniqueIdentifier = () => {
  if (IS_DEV) {
    return 'com.ogi.wideas.OperationGo.dev';
  }

  if (IS_PREVIEW) {
    return 'com.ogi.wideas.OperationGo.beta';
  }

  return 'com.ogi.wideas.OperationGo';
};

const getVersionExtension = () => {
  if (IS_DEV) {
    return '-dev';
  }

  if (IS_PREVIEW) {
    return '-beta';
  }

  return '';
};

const getAppName = () => {
  if (IS_DEV) {
    return 'Operation Go (Dev)';
  }

  if (IS_PREVIEW) {
    return 'Operation Go (Beta)';
  }

  return 'Operation Go';
};

export default {
  expo: {
    name: getAppName(),
    slug: 'ogi-app',
    version: '1.241111.0' + getVersionExtension(),
    orientation: 'portrait',
    icon: './assets/images/icon.png',
    scheme: 'ogiapp',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: getUniqueIdentifier(),
    },
    android: {
      adaptiveIcon: {
        backgroundImage: './assets/images/adaptive-bg.png',
        foregroundImage: './assets/images/adaptive-icon.png',
        backgroundColor: '#DCF1FB',
      },
      package: getUniqueIdentifier(),
      intentFilters: [
        {
          action: 'VIEW',
          autoVerify: true,
          data: [
            {
              scheme: 'ogiapp',
              host: 'callback',
              pathPrefix: '/',
            },
          ],
          category: ['BROWSABLE', 'DEFAULT'],
        },
      ],
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png',
    },
    plugins: [
      'expo-font',
      'expo-router',
      [
        'expo-splash-screen',
        {
          image: './assets/images/splash-icon.png',
          imageWidth: 200,
          resizeMode: 'contain',
          backgroundColor: '#DCF1FB',
        },
      ],
      ['react-native-edge-to-edge', { android: { parentTheme: 'Material3' } }],
      'expo-secure-store',
      'expo-web-browser',
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      eas: {
        projectId: 'c00f650d-fbca-4093-b68c-5143267a7025',
      },
      kindeDomain: process.env.KINDE_DOMAIN,
      kindeClientId: process.env.KINDE_CLIENT_ID,
    },
    owner: 'wideas',
  },
};
