const { getDefaultConfig } = require('expo/metro-config');

module.exports = (() => {
  const config = getDefaultConfig(__dirname);

  // Add ESM support for .mjs files
  const { resolver } = config;

  // Force metro to handle .mjs files as regular JavaScript
  resolver.sourceExts = [...resolver.sourceExts, 'mjs'];

  // Add alias to handle missing react-native-unistyles components
  resolver.alias = {
    ...resolver.alias,
    'react-native-unistyles/components/native/View': require.resolve(
      'react-native/Libraries/Components/View/View.js',
    ),
  };

  // Configure metro to handle ESM
  config.transformer = {
    ...config.transformer,
    asyncRequireModulePath: require.resolve('metro-runtime/src/modules/asyncRequire'),
    // Enable hermes parsing for all files
    hermesParser: true,
    // Ensure we use commonjs modules
    unstable_transformProfile: 'hermes-stable',
  };

  return config;
})();
