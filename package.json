{"name": "ogi-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "APP_VARIANT=development npx expo run", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "format": "prettier --write src/**/*.{js,jsx,ts,tsx}", "format:check": "prettier --check src/**/*.{js,jsx,ts,tsx}", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write src/**/*.{js,jsx,ts,tsx}"]}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/source-sans-3": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^3.9.1", "@kinde-oss/react-native-sdk-0-7x": "^2.0.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@tanstack/query-core": "^5.61.5", "@tanstack/react-query": "^5.61.4", "@tanstack/react-query-persist-client": "^5.85.5", "axios": "^1.7.8", "expo": "53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "jotai": "^2.10.3", "lucide-react-native": "^0.456.0", "moti": "^0.29.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.1", "react-native": "0.79.5", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-mmkv": "^3.1.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.9", "react-native-unistyles": "^2.12.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "valibot": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@dev-plugins/react-native-mmkv": "~0.2.0", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~53.0.9", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true, "trustedDependencies": ["react-native-inappbrowser-reborn"]}