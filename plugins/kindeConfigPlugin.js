const { withPlugins } = require('@expo/config-plugins');

const withKinde = (config, { domain }) => {
  return withPlugins(config, [
    // Here we would define native configuration changes.
    // Since Kinde specifics aren't detailed, we'll simulate a basic setup:
    (config) => {
      if (config.ios) {
        config.ios.infoPlist = {
          ...config.ios.infoPlist,
          // Add any iOS specific configurations here
          KINDE_ISSUER_URL: `https://${domain}.kinde.com`,
          KINDE_CLIENT_ID: '5d45605f43f64366b1416f9533d3954a',
        };
      }
      if (config.android) {
        config.android.manifest = {
          ...config.android.manifest,
          application: [
            {
              // $: {
              //   'android:name': '.MainApplication',
              //   'android:label': '@string/app_name',
              // },
              // Add any Android specific configurations here
              metaData: [
                {
                  $: {
                    'android:name': 'KINDE_ISSUER_URL',
                    'android:value': `https://${domain}.kinde.com`,
                  },
                },
                {
                  $: {
                    'android:name': 'KINDE_CLIENT_ID',
                    'android:value': '5d45605f43f64366b1416f9533d3954a',
                  },
                },
              ],
            },
          ],
        };
      }
      return config;
    },
  ]);
};

module.exports = withKinde;
