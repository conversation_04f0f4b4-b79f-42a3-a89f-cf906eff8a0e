// plugins/withKindeAuth.js
const { withAppBuildGradle, withInfoPlist, withXcodeProject } = require('@expo/config-plugins');

module.exports = function withKindeAuth(config) {
  // Android: Modify build.gradle for required Kinde dependencies
  config = withAppBuildGradle(config, (config) => {
    config.modResults.contents += `
    dependencies {
      implementation "com.kinde.sdk:auth-library:1.0.0"
    }`;
    return config;
  });

  // iOS: Modify Info.plist for required Kinde configurations
  config = withInfoPlist(config, (config) => {
    config.modResults.KindeAuthKey = 'YOUR_KINDE_KEY'; // Replace with actual key
    return config;
  });

  // Additional iOS modifications (e.g., XcodeProject tweaks)
  config = withXcodeProject(config, (config) => {
    // Add custom settings or libraries
    return config;
  });

  return config;
};
