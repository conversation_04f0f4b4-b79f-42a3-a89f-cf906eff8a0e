import axios from 'axios';

import { kindeAuth } from '@/libs/auth/kindeAuth';

const api = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Define authenticated routes patterns
const authenticatedRoutes = [
  '/users',
  '/prospects',
  // Add other authenticated endpoints as needed
];

api.interceptors.request.use(
  async (config) => {
    // Check if the request URL matches any authenticated route pattern
    const requiresAuth = authenticatedRoutes.some((route) => config.url?.startsWith(route));

    if (requiresAuth) {
      try {
        const token = await kindeAuth.getValidAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('❌ Failed to get auth token:', error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

export { api };
