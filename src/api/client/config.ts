// Environment configuration for API client
export const API_CONFIG = {
  // Base URLs
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8080/api/v1',

  // Timeout settings
  timeout: 10000,

  // Retry configuration
  retry: {
    attempts: 3,
    delay: 1000, // ms
    backoff: 'exponential' as const,
  },

  // Cache configuration aligned with Go backend Redis TTL
  cache: {
    staleTime: 15 * 60 * 1000, // 15 minutes (matches Go backend)
    cacheTime: 24 * 60 * 60 * 1000, // 24 hours for MMKV persistence
    refetchOnReconnect: true,
    refetchOnWindowFocus: false,
  },

  // Headers
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
} as const;

// Environment-specific overrides
export const getApiConfig = () => {
  const isDevelopment = __DEV__;

  return {
    ...API_CONFIG,
    // Enable request logging in development
    enableLogging: isDevelopment,
  };
};
