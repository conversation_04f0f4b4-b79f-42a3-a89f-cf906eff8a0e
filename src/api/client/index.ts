import axios, { AxiosInstance } from 'axios';

import { getApiConfig } from './config';
import {
  createRequestInterceptor,
  createResponseInterceptor,
  createRetryInterceptor,
} from './interceptors';

// Create enhanced API client
const createApi = (): AxiosInstance => {
  const config = getApiConfig();

  const client = axios.create({
    baseURL: config.baseURL,
    timeout: config.timeout,
    headers: config.headers,
  });

  // Add request interceptor
  client.interceptors.request.use(createRequestInterceptor(), Promise.reject);

  // Add response interceptors
  const { onSuccess, onError } = createResponseInterceptor();
  client.interceptors.response.use(onSuccess, onError);

  // Add retry interceptor
  client.interceptors.response.use(undefined, createRetryInterceptor());

  return client;
};

// Export the configured API client instance
export const api = createApi();

// Re-export types for convenience
export * from './types';
export * from './config';
