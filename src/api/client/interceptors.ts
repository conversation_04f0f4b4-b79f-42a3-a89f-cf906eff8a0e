import { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

import { kindeAuth } from '@/libs/auth/kindeAuth';

import { API_CONFIG, getApiConfig } from './config';
import { getErrorMessage, isApiError } from './types';

// Request interceptor for authentication and logging
export const createRequestInterceptor = () => {
  return async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    const apiConfig = getApiConfig();

    // Add authentication for protected routes
    const authenticatedRoutes = ['/users', '/prospects'];
    const requiresAuth = authenticatedRoutes.some((route) => config.url?.startsWith(route));

    if (requiresAuth) {
      try {
        const token = await kindeAuth.getValidAccessToken();
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('Failed to get access token:', error);
      }
    }

    // Add request ID for tracing
    if (config.headers) {
      config.headers['X-Request-ID'] = generateRequestId();
    }

    // Log requests in development
    if (apiConfig.enableLogging) {
      console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }

    return config;
  };
};

// Response interceptor for error handling and logging
export const createResponseInterceptor = () => {
  const onSuccess = (response: AxiosResponse) => {
    const apiConfig = getApiConfig();

    // Log successful responses in development
    if (apiConfig.enableLogging) {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    }

    return response;
  };

  const onError = (error: AxiosError) => {
    const apiConfig = getApiConfig();

    // Log errors in development
    if (apiConfig.enableLogging) {
      console.error(`❌ API Error: ${error.response?.status} ${error.config?.url}`, {
        message: getErrorMessage(error.response?.data),
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    // Handle specific error cases
    if (error.response?.status === 401) {
      // Token expired or invalid - could trigger auth refresh here
      console.warn('Unauthorized request - token may be expired');
    }

    // Ensure consistent error format
    const apiError = error.response?.data;
    if (isApiError(apiError)) {
      return Promise.reject(apiError);
    }

    // Fallback error structure
    return Promise.reject({
      error: {
        type: 'NETWORK_ERROR',
        code: 'REQUEST_FAILED',
        message: getErrorMessage(error),
        request_id: error.response?.headers['x-request-id'],
      },
    });
  };

  return { onSuccess, onError };
};

// Request interceptor for retry logic
export const createRetryInterceptor = () => {
  return (error: AxiosError) => {
    const config = error.config as any;

    // Initialize retry count
    config.__retryCount = config.__retryCount || 0;

    // Check if we should retry
    const shouldRetry = config.__retryCount < API_CONFIG.retry.attempts && isRetriableError(error);

    if (!shouldRetry) {
      return Promise.reject(error);
    }

    // Increment retry count
    config.__retryCount += 1;

    // Calculate delay (exponential backoff)
    const delay = API_CONFIG.retry.delay * Math.pow(2, config.__retryCount - 1);

    // Wait and retry
    return new Promise((resolve) => {
      setTimeout(() => resolve(config), delay);
    });
  };
};

// Helper functions
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
};

const isRetriableError = (error: AxiosError): boolean => {
  if (!error.response) return true; // Network errors are retriable

  const status = error.response.status;
  return status >= 500 || status === 408 || status === 429;
};
