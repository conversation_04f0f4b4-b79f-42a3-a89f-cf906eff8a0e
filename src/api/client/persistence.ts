import { MMKV } from 'react-native-mmkv';

import {
  type PersistedClient,
  type Persister,
  persistQueryClientRestore,
  persistQueryClientSave,
} from '@tanstack/react-query-persist-client';

// Create dedicated MMKV instance for TanStack Query cache
const queryStorage = new MMKV({
  id: 'tanstack-query-cache',
  // Optional: Add encryption key for sensitive data
  // encryptionKey: 'your-encryption-key-here'
});

// Storage interface that TanStack Query persister expects
const mmkvStorage = {
  getItem: (key: string): string | null => {
    try {
      const value = queryStorage.getString(key);
      return value ?? null;
    } catch (error) {
      console.warn('MMKV getItem error:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): void => {
    try {
      queryStorage.set(key, value);
    } catch (error) {
      console.warn('MMKV setItem error:', error);
    }
  },

  removeItem: (key: string): void => {
    try {
      queryStorage.delete(key);
    } catch (error) {
      console.warn('MMKV removeItem error:', error);
    }
  },
};

// Create the persister for TanStack Query
export const queryPersister: Persister = {
  persistClient: async (persistedClient: PersistedClient) => {
    mmkvStorage.setItem('tanstack-query-cache', JSON.stringify(persistedClient));
  },

  restoreClient: async () => {
    const clientStr = mmkvStorage.getItem('tanstack-query-cache');
    return clientStr ? JSON.parse(clientStr) : undefined;
  },

  removeClient: async () => {
    mmkvStorage.removeItem('tanstack-query-cache');
  },
};

// Cache management utilities for debugging
export const cacheUtils = {
  clearCache: (): void => {
    try {
      queryStorage.clearAll();
      console.log('TanStack Query cache cleared');
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  },

  getCacheSize: (): number => {
    try {
      return queryStorage.size;
    } catch (error) {
      console.warn('Cache size error:', error);
      return 0;
    }
  },

  getAllKeys: (): string[] => {
    try {
      return queryStorage.getAllKeys();
    } catch (error) {
      console.warn('Get all keys error:', error);
      return [];
    }
  },

  getCacheKey: (key: string): string | null => {
    return mmkvStorage.getItem(key);
  },
};
