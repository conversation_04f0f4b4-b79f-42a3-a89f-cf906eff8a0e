// Common API types and utilities

// Base API response structure
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Error types matching Go backend error structure
export interface ApiError {
  type: string;
  code: string;
  message: string;
  request_id?: string;
  timestamp?: string;
}

export interface ApiErrorResponse {
  error: ApiError;
}

// Request configuration
export interface ApiRequestConfig {
  timeout?: number;
  retry?: boolean;
  cache?: boolean;
  requiresAuth?: boolean;
}

// Type guards for API responses
export const isApiError = (response: any): response is ApiErrorResponse => {
  return response && typeof response === 'object' && 'error' in response;
};

export const isApiSuccess = <T>(response: any): response is ApiResponse<T> => {
  return response && typeof response === 'object' && 'data' in response;
};

// Utility function to extract error message
export const getErrorMessage = (error: unknown): string => {
  if (isApiError(error)) {
    return error.error.message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unexpected error occurred';
};
