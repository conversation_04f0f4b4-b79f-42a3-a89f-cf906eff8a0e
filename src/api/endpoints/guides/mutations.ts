// Guide mutation endpoints (for future CRUD operations)
import { api } from '../../client';
import {
  CreateSectionRequest,
  CreateStepRequest,
  SectionDetail,
  StepDetail,
  UpdateSectionRequest,
  UpdateStepRequest,
} from '../../schemas/guides';

// Section mutations
export const createSection = async (data: CreateSectionRequest) => {
  const response = await api.post<SectionDetail>('/guides/sections', data);
  return response;
};

export const updateSection = async (data: UpdateSectionRequest) => {
  const { id, ...updateData } = data;
  const response = await api.put<SectionDetail>(`/guides/sections/${id}`, updateData);
  return response;
};

export const deleteSection = async (id: string) => {
  const response = await api.delete(`/guides/sections/${id}`);
  return response;
};

// Step mutations
export const createStep = async (data: CreateStepRequest) => {
  const response = await api.post<StepDetail>('/guides/steps', data);
  return response;
};

export const updateStep = async (data: UpdateStepRequest) => {
  const { id, ...updateData } = data;
  const response = await api.put<StepDetail>(`/guides/steps/${id}`, updateData);
  return response;
};

export const deleteStep = async (id: string) => {
  const response = await api.delete(`/guides/steps/${id}`);
  return response;
};
