// Guide query endpoints - clean API calls to Go backend
import { api } from '../../client';
import {
  GetSectionsParams,
  GetStepsParams,
  SectionDetail,
  SectionsListResponse,
  StepDetail,
} from '../../schemas/guides';

// Get all sections
export const getSections = async (params?: GetSectionsParams) => {
  const response = await api.get<SectionsListResponse>('/guide/sections', {
    params: {
      page: params?.page,
      limit: params?.limit,
      sort: params?.sort || 'sectionNo',
    },
  });

  return response.data;
};

// Get section by sectionNo with steps (Go backend uses sectionNo, not ID)
export const getSection = async (sectionNo: string) => {
  const response = await api.get<SectionDetail>(`/guide/sections/${sectionNo}`);
  return response.data;
};

// Get step by stepNo (Go backend uses stepNo, not ID)
export const getStep = async (stepNo: string) => {
  const response = await api.get<StepDetail>(`/guide/steps/${stepNo}`);
  return response.data;
};

// Get steps for a specific section (alternative endpoint if needed)
export const getStepsForSection = async (sectionId: string, params?: GetStepsParams) => {
  const response = await api.get<StepDetail[]>('/guides/steps', {
    params: {
      sectionId,
      page: params?.page,
      limit: params?.limit,
      sort: params?.sort || 'stepNo',
    },
  });

  return response.data;
};
