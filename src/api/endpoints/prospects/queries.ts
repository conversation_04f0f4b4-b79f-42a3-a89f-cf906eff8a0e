/**
 * Prospects API queries - Go backend integration
 * Following Gospel feature pattern with enhanced axios client
 */
import { api } from '@/api/client';
import {
  type CreateProspectRequest,
  type DashboardResponse,
  type ProspectDetailResponse,
  type ProspectListResponse,
  type ProspectStatsResponse,
  type SearchProspectsResponse,
  type UpdateProspectRequest,
  type UpdateStatusRequest,
} from '@/api/schemas/prospects';

/**
 * Get user's prospect list
 * Backend automatically scopes to authenticated user
 */
export const getProspects = async (): Promise<ProspectListResponse> => {
  const response = await api.get<ProspectListResponse>('/prospects');
  return response.data;
};

/**
 * Search user's prospects by text query
 * Searches across firstName, nickName, lastName, notes, contactInfo
 */
export const searchProspects = async (query: string): Promise<SearchProspectsResponse> => {
  const response = await api.get<SearchProspectsResponse>('/prospects/search', {
    params: { q: query },
  });
  return response.data;
};

/**
 * Get detailed prospect information
 */
export const getProspectDetail = async (id: string): Promise<ProspectDetailResponse> => {
  const response = await api.get<ProspectDetailResponse>(`/prospects/${id}`);
  return response.data;
};

/**
 * Get dashboard data with weekly summary and priority reminders
 */
export const getDashboard = async (): Promise<DashboardResponse> => {
  const response = await api.get<DashboardResponse>('/prospects/dashboard');
  return response.data;
};

/**
 * Get weekly/monthly statistics
 */
export const getProspectStats = async (): Promise<ProspectStatsResponse> => {
  const response = await api.get<ProspectStatsResponse>('/prospects/stats');
  return response.data;
};

/**
 * Create new prospect
 * Backend automatically assigns to authenticated user
 */
export const createProspect = async (
  prospectData: CreateProspectRequest,
): Promise<ProspectDetailResponse> => {
  const response = await api.post<ProspectDetailResponse>('/prospects', prospectData);
  return response.data;
};

/**
 * Update prospect information
 */
export const updateProspect = async (
  id: string,
  updateData: UpdateProspectRequest,
): Promise<ProspectDetailResponse> => {
  const response = await api.put<ProspectDetailResponse>(`/prospects/${id}`, updateData);
  return response.data;
};

/**
 * Update only prospect status with validation
 * Backend enforces progression rules (PROSPECT → SAVED → BAPTIZED → FOUNDATION → MINISTRY)
 */
export const updateProspectStatus = async (
  id: string,
  statusData: UpdateStatusRequest,
): Promise<ProspectDetailResponse> => {
  const response = await api.put<ProspectDetailResponse>(`/prospects/${id}/status`, statusData);
  return response.data;
};

/**
 * Delete prospect
 * Permanently removes prospect from user's list
 */
export const deleteProspect = async (id: string): Promise<void> => {
  await api.delete(`/prospects/${id}`);
};
