import {
  UseMutationOptions,
  UseMutationResult,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query';
import { AxiosResponse } from 'axios';

import { api } from '../client';
import { ApiErrorResponse, getErrorMessage, isApiError } from '../client/types';

// Enhanced useApiMutation hook with optimistic updates and error handling
export function useApiMutation<
  TData = unknown,
  TError = ApiErrorResponse,
  TVariables = void,
  TContext = unknown,
>(
  mutationFn: (variables: TVariables) => Promise<AxiosResponse<TData>>,
  options?: Omit<
    UseMutationOptions<AxiosResponse<TData>, TError, TVariables, TContext>,
    'mutationFn'
  >,
): UseMutationResult<AxiosResponse<TData>, TError, TVariables, TContext> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    // Enhanced error handling
    onError: (error, variables, context) => {
      console.error('API Mutation Error:', {
        variables,
        message: getErrorMessage(error),
        error,
      });

      // Call user's onError if provided
      options?.onError?.(error, variables, context);
    },
    // Success logging
    onSuccess: (data, variables, context) => {
      console.log('API Mutation Success:', {
        variables,
        status: data.status,
      });

      // Call user's onSuccess if provided
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

// Specialized mutation hooks

// Hook for creating resources with optimistic updates
export function useCreateMutation<TData = unknown, TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<AxiosResponse<TData>>,
  {
    invalidateQueries,
    optimisticUpdate,
    ...options
  }: {
    invalidateQueries?: string[][];
    optimisticUpdate?: {
      queryKey: string[];
      updateFn: (old: any, variables: TVariables) => any;
    };
  } & Omit<
    UseMutationOptions<AxiosResponse<TData>, ApiErrorResponse, TVariables, unknown>,
    'mutationFn'
  > = {},
) {
  const queryClient = useQueryClient();

  return useApiMutation(mutationFn, {
    onMutate: async (variables) => {
      // Optimistic update
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);

        queryClient.setQueryData(optimisticUpdate.queryKey, (old: any) =>
          optimisticUpdate.updateFn(old, variables),
        );

        return { previousData };
      }

      return options?.onMutate?.(variables);
    },
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries?.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey });
      });

      options?.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (optimisticUpdate && context && typeof context === 'object' && 'previousData' in context) {
        queryClient.setQueryData(optimisticUpdate.queryKey, context.previousData);
      }

      options?.onError?.(error, variables, context);
    },
    ...options,
  });
}

// Hook for updating resources with optimistic updates
export function useUpdateMutation<TData = unknown, TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<AxiosResponse<TData>>,
  {
    invalidateQueries,
    optimisticUpdate,
    ...options
  }: {
    invalidateQueries?: string[][];
    optimisticUpdate?: {
      queryKey: string[];
      updateFn: (old: any, variables: TVariables) => any;
    };
  } & Omit<
    UseMutationOptions<AxiosResponse<TData>, ApiErrorResponse, TVariables, unknown>,
    'mutationFn'
  > = {},
) {
  const queryClient = useQueryClient();

  return useApiMutation(mutationFn, {
    onMutate: async (variables) => {
      // Optimistic update
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);

        queryClient.setQueryData(optimisticUpdate.queryKey, (old: any) =>
          optimisticUpdate.updateFn(old, variables),
        );

        return { previousData };
      }

      return options?.onMutate?.(variables);
    },
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries?.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey });
      });

      options?.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (optimisticUpdate && context && typeof context === 'object' && 'previousData' in context) {
        queryClient.setQueryData(optimisticUpdate.queryKey, context.previousData);
      }

      options?.onError?.(error, variables, context);
    },
    ...options,
  });
}

// Hook for deleting resources with optimistic updates
export function useDeleteMutation<TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<AxiosResponse<void>>,
  {
    invalidateQueries,
    optimisticUpdate,
    ...options
  }: {
    invalidateQueries?: string[][];
    optimisticUpdate?: {
      queryKey: string[];
      updateFn: (old: any, variables: TVariables) => any;
    };
  } & Omit<
    UseMutationOptions<AxiosResponse<void>, ApiErrorResponse, TVariables, unknown>,
    'mutationFn'
  > = {},
) {
  const queryClient = useQueryClient();

  return useApiMutation(mutationFn, {
    onMutate: async (variables) => {
      // Optimistic update
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);

        queryClient.setQueryData(optimisticUpdate.queryKey, (old: any) =>
          optimisticUpdate.updateFn(old, variables),
        );

        return { previousData };
      }

      return options?.onMutate?.(variables);
    },
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      invalidateQueries?.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey });
      });

      options?.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (optimisticUpdate && context && typeof context === 'object' && 'previousData' in context) {
        queryClient.setQueryData(optimisticUpdate.queryKey, context.previousData);
      }

      options?.onError?.(error, variables, context);
    },
    ...options,
  });
}
