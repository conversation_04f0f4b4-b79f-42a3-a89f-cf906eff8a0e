import { UseQueryOptions, UseQueryResult, useQuery } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';

import { API_CONFIG } from '../client/config';
import { ApiErrorResponse, isApiError } from '../client/types';

// Cache keys aligned with Go backend Redis keys
export const CACHE_KEYS = {
  guides: {
    sections: 'guide:sections:list',
    section: (id: string) => `guide:section:${id}`,
    step: (id: string) => `guide:step:${id}`,
  },
  // Prospects cache keys (aligned with backend Redis keys)
  prospects: {
    list: 'prospects:user:list',
    detail: (id: string) => `prospect:detail:${id}`,
    dashboard: 'prospects:user:dashboard',
    stats: 'prospects:user:stats',
    search: 'prospects:search',
  },
  users: {
    profile: 'user:profile',
  },
} as const;

// Enhanced useApiQuery hook with defaults optimized for mobile
export function useApiQuery<
  TData = unknown,
  TSelectData = AxiosResponse<TData>,
  TError = ApiErrorResponse,
>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<AxiosResponse<TData>>,
  options?: Omit<
    UseQueryOptions<AxiosResponse<TData>, TError, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn'
  >,
): UseQueryResult<TSelectData, TError> {
  return useQuery({
    queryKey,
    queryFn,
    // Default options optimized for mobile + offline
    staleTime: API_CONFIG.cache.staleTime,
    gcTime: API_CONFIG.cache.cacheTime, // TanStack Query v5 renamed cacheTime to gcTime
    refetchOnReconnect: API_CONFIG.cache.refetchOnReconnect,
    refetchOnWindowFocus: API_CONFIG.cache.refetchOnWindowFocus,
    // Retry configuration
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (isApiError(error) && error.error?.code?.startsWith('4')) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // TanStack Query v5: onError is deprecated, handle errors in components
    ...options,
  });
}

// Specialized hooks for common patterns

// Hook for paginated queries
export function usePaginatedQuery<TData = unknown, TSelectData = AxiosResponse<TData>>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<AxiosResponse<TData>>,
  options?: Omit<
    UseQueryOptions<AxiosResponse<TData>, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn'
  >,
) {
  return useApiQuery(queryKey, queryFn, {
    // Keep paginated data longer
    staleTime: API_CONFIG.cache.staleTime * 2,
    ...options,
  });
}

// Hook for frequently updated data
export function useRealtimeQuery<TData = unknown, TSelectData = AxiosResponse<TData>>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<AxiosResponse<TData>>,
  options?: Omit<
    UseQueryOptions<AxiosResponse<TData>, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn'
  >,
) {
  return useApiQuery(queryKey, queryFn, {
    // More frequent updates
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    ...options,
  });
}

// Hook for cached reference data
export function useStaticQuery<TData = unknown, TSelectData = AxiosResponse<TData>>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<AxiosResponse<TData>>,
  options?: Omit<
    UseQueryOptions<AxiosResponse<TData>, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn'
  >,
) {
  return useApiQuery(queryKey, queryFn, {
    // Cache static data much longer
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 7 * 24 * 60 * 60 * 1000, // 1 week
    refetchOnMount: false,
    refetchOnReconnect: false,
    ...options,
  });
}
