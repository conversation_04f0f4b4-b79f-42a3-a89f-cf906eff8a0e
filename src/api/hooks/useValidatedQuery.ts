// Enhanced API hook with Valibot runtime validation
import { UseQueryOptions } from '@tanstack/react-query';
import * as v from 'valibot';

import { ApiErrorResponse } from '../client/types';
import { useApiQuery, useStaticQuery } from './useApiQuery';

// Generic validated query hook
export const useValidatedQuery = <
  TSchema extends v.BaseSchema<any, any, any>,
  TSelectData = v.InferOutput<TSchema>,
>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<any>,
  schema: TSchema,
  options?: Omit<
    UseQueryOptions<any, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn' | 'select'
  > & {
    select?: (data: v.InferOutput<TSchema>) => TSelectData;
  },
) => {
  const { select, ...queryOptions } = options || {};

  return useApiQuery<any, TSelectData, ApiErrorResponse>(
    queryKey,
    async () => {
      const data = await queryFn();

      // Validate data against schema
      try {
        const validatedData = v.parse(schema, data);
        return validatedData;
      } catch (error) {
        // Enhanced error logging with context
        const validationError = error as v.ValiError<TSchema>;
        console.error('API Validation Error:', {
          endpoint: queryKey.join(':'),
          message: validationError.message,
          issues: validationError.issues?.map((issue) => ({
            path: issue.path?.map((p: any) => p.key).join('.'),
            message: issue.message,
            expected: issue.expected,
            received: issue.received,
          })),
        });

        // Re-throw with context for better debugging
        throw new Error(
          `API validation failed for ${queryKey.join(':')}: ${validationError.message}`,
        );
      }
    },
    {
      ...queryOptions,
      select: select ? (data) => select(data) : undefined,
    },
  );
};

// Specialized validated query for static/cached data
export const useValidatedStaticQuery = <
  TSchema extends v.BaseSchema<any, any, any>,
  TSelectData = v.InferOutput<TSchema>,
>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<any>,
  schema: TSchema,
  options?: Omit<
    UseQueryOptions<any, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn' | 'select'
  > & {
    select?: (data: v.InferOutput<TSchema>) => TSelectData;
  },
) => {
  const { select, ...queryOptions } = options || {};

  return useStaticQuery<any, TSelectData>(
    queryKey,
    async () => {
      const data = await queryFn();

      // Validate data against schema
      try {
        const validatedData = v.parse(schema, data);
        return validatedData;
      } catch (error) {
        // Enhanced error logging with context
        const validationError = error as v.ValiError<TSchema>;
        console.error('API Validation Error:', {
          endpoint: queryKey.join(':'),
          message: validationError.message,
          issues: validationError.issues?.map((issue) => ({
            path: issue.path?.map((p: any) => p.key).join('.'),
            message: issue.message,
            expected: issue.expected,
            received: issue.received,
          })),
        });

        // Re-throw with context for better debugging
        throw new Error(
          `API validation failed for ${queryKey.join(':')}: ${validationError.message}`,
        );
      }
    },
    {
      ...queryOptions,
      select: select ? (data) => select(data) : undefined,
    },
  );
};

// Specialized validated query for Gospel content with infinite persistence
export const useValidatedGospelQuery = <
  TSchema extends v.BaseSchema<any, any, any>,
  TSelectData = v.InferOutput<TSchema>,
>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<any>,
  schema: TSchema,
  options?: Omit<
    UseQueryOptions<any, ApiErrorResponse, TSelectData, readonly unknown[]>,
    'queryKey' | 'queryFn' | 'select'
  > & {
    select?: (data: v.InferOutput<TSchema>) => TSelectData;
  },
) => {
  const { select, ...queryOptions } = options || {};

  return useApiQuery<any, TSelectData>(
    queryKey,
    async () => {
      const data = await queryFn();

      // Validate data against schema
      try {
        const validatedData = v.parse(schema, data);
        return validatedData;
      } catch (error) {
        // Enhanced error logging with context
        const validationError = error as v.ValiError<TSchema>;
        console.error('API Validation Error:', {
          endpoint: queryKey.join(':'),
          message: validationError.message,
          issues: validationError.issues?.map((issue) => ({
            path: issue.path?.map((p: any) => p.key).join('.'),
            message: issue.message,
            expected: issue.expected,
            received: issue.received,
          })),
        });

        // Re-throw with context for better debugging
        throw new Error(
          `API validation failed for ${queryKey.join(':')}: ${validationError.message}`,
        );
      }
    },
    {
      // Gospel-specific settings for infinite persistence
      gcTime: Infinity, // Never expires - available indefinitely for offline ministry
      staleTime: 24 * 60 * 60 * 1000, // Still checks for updates daily when online
      refetchOnReconnect: true, // Automatic fresh data when back online
      refetchOnMount: false, // Don't refetch on mount - use cached data
      refetchOnWindowFocus: false, // Don't refetch on focus
      ...queryOptions,
      select: select ? (data) => select(data) : undefined,
    },
  );
};
