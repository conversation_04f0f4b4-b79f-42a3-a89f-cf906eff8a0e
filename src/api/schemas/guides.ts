// Valibot schemas for Gospel feature API validation
import * as v from 'valibot';

// Image schema with runtime validation to catch property mismatches
export const ImageSchema = v.object({
  url: v.string(), // ✅ Catches uri → url changes
  blurHash: v.string(),
  altText: v.optional(v.string()),
  description: v.optional(v.string()),
});

// Bible verse schema
export const BibleVerseSchema = v.object({
  text: v.string(),
  verse: v.number(), // ✅ Catches num → verse changes
});

// Bible reference schema
export const BibleReferenceSchema = v.object({
  reference: v.string(), // ✅ Catches referenceText → reference changes
  verses: v.array(BibleVerseSchema),
});

// Golden key schema
export const GoldenKeySchema = v.object({
  number: v.number(), // ✅ Catches num → number changes
  key: v.string(),
  expanded: v.optional(v.string()),
});

// Step schema with comprehensive validation
export const StepSchema = v.object({
  title: v.string(),
  stepNo: v.number(),
  aim: v.string(),
  hint: v.optional(v.string()),
  content: v.optional(v.string()),
  goldenKey: v.optional(GoldenKeySchema),
  bibleReferences: v.optional(v.array(BibleReferenceSchema)),
});

// Step detail schema (same as StepSchema for now)
export const StepDetailSchema = StepSchema;

// Section schemas
export const SectionSummarySchema = v.object({
  title: v.string(),
  sectionNo: v.number(),
  subtitle: v.string(),
  image: v.optional(ImageSchema), // ✅ Catches image property changes
  hint: v.string(),
  overview: v.string(),
});

export const SectionDetailSchema = v.object({
  title: v.string(),
  sectionNo: v.number(),
  subtitle: v.string(),
  image: v.optional(ImageSchema),
  hint: v.string(),
  overview: v.string(),
  steps: v.array(StepSchema),
});

// API response schemas
export const SectionsListResponseSchema = v.object({
  sections: v.array(SectionSummarySchema),
  total: v.number(),
});

// Request schemas for mutations
export const CreateSectionRequestSchema = v.object({
  title: v.string(),
  subtitle: v.string(),
  hint: v.string(),
  overview: v.string(),
  sectionNo: v.number(),
});

export const UpdateSectionRequestSchema = v.object({
  id: v.string(),
  title: v.optional(v.string()),
  subtitle: v.optional(v.string()),
  hint: v.optional(v.string()),
  overview: v.optional(v.string()),
  sectionNo: v.optional(v.number()),
});

export const CreateStepRequestSchema = v.object({
  title: v.string(),
  aim: v.string(),
  hint: v.optional(v.string()),
  content: v.optional(v.string()),
  stepNo: v.number(),
  sectionId: v.string(),
});

export const UpdateStepRequestSchema = v.object({
  id: v.string(),
  title: v.optional(v.string()),
  aim: v.optional(v.string()),
  hint: v.optional(v.string()),
  content: v.optional(v.string()),
  stepNo: v.optional(v.number()),
  sectionId: v.optional(v.string()),
});

// Query parameter schemas
export const GetSectionsParamsSchema = v.object({
  page: v.optional(v.number()),
  limit: v.optional(v.number()),
  sort: v.optional(v.union([v.literal('sectionNo'), v.literal('-sectionNo')])),
});

export const GetStepsParamsSchema = v.object({
  sectionId: v.optional(v.string()),
  page: v.optional(v.number()),
  limit: v.optional(v.number()),
  sort: v.optional(v.union([v.literal('stepNo'), v.literal('-stepNo')])),
});

// Export inferred types for backward compatibility
export type Image = v.InferInput<typeof ImageSchema>;
export type BibleVerse = v.InferInput<typeof BibleVerseSchema>;
export type BibleReference = v.InferInput<typeof BibleReferenceSchema>;
export type GoldenKey = v.InferInput<typeof GoldenKeySchema>;
export type Step = v.InferInput<typeof StepSchema>;
export type StepDetail = v.InferInput<typeof StepDetailSchema>;
export type SectionSummary = v.InferInput<typeof SectionSummarySchema>;
export type SectionDetail = v.InferInput<typeof SectionDetailSchema>;
export type SectionsListResponse = v.InferInput<typeof SectionsListResponseSchema>;

// Request types
export type CreateSectionRequest = v.InferInput<typeof CreateSectionRequestSchema>;
export type UpdateSectionRequest = v.InferInput<typeof UpdateSectionRequestSchema>;
export type CreateStepRequest = v.InferInput<typeof CreateStepRequestSchema>;
export type UpdateStepRequest = v.InferInput<typeof UpdateStepRequestSchema>;

// Query parameter types
export type GetSectionsParams = v.InferInput<typeof GetSectionsParamsSchema>;
export type GetStepsParams = v.InferInput<typeof GetStepsParamsSchema>;
