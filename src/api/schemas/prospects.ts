/**
 * Valibot schemas for Prospects API - Runtime validation and type inference
 * Following Go backend DTOs from backend handoff document
 */
import * as v from 'valibot';

// Core Prospect Status enum
export const ProspectStatusSchema = v.union([
  v.literal('PROSPECT'),
  v.literal('SAVED'),
  v.literal('BAPTIZED'),
  v.literal('FOUNDATION'),
  v.literal('MINISTRY'),
]);

export type ProspectStatus = v.InferOutput<typeof ProspectStatusSchema>;

// Person Name structure
export const PersonNameSchema = v.object({
  firstName: v.string(),
  nickName: v.optional(v.string()),
  lastName: v.string(),
  fullName: v.string(), // Computed by backend
});

export type PersonName = v.InferOutput<typeof PersonNameSchema>;

// Full Prospect entity
export const ProspectSchema = v.object({
  id: v.string(),
  name: PersonNameSchema,
  contactInfo: v.string(),
  address: v.string(),
  notes: v.string(),
  status: ProspectStatusSchema,
  createdBy: v.string(),
  createdAt: v.string(),
  updatedAt: v.string(),
  lastContactAt: v.optional(v.string()),
});

export type Prospect = v.InferOutput<typeof ProspectSchema>;

// Prospect List Item (optimized DTO)
export const ProspectListItemSchema = v.object({
  id: v.string(),
  displayName: v.string(), // nickName || firstName + lastName
  status: ProspectStatusSchema,
  daysSinceUpdate: v.number(),
  statusIcon: v.string(), // "👤", "✝️", "💧", "📚", "⚡"
  lastContactAt: v.string(),
});

export type ProspectListItem = v.InferOutput<typeof ProspectListItemSchema>;

// API Response schemas
export const ProspectListResponseSchema = v.object({
  prospects: v.array(ProspectListItemSchema),
});

export type ProspectListResponse = v.InferOutput<typeof ProspectListResponseSchema>;

export const ProspectDetailResponseSchema = ProspectSchema;
export type ProspectDetailResponse = v.InferOutput<typeof ProspectDetailResponseSchema>;

// Dashboard schemas
export const PriorityReminderSchema = v.object({
  prospectId: v.string(),
  prospectName: v.string(),
  message: v.string(), // "Follow up with John (7 days ago)"
  type: v.union([v.literal('follow_up'), v.literal('baptism_ready'), v.literal('ministry_ready')]),
  icon: v.string(), // "🔔", "⛪", "⚡"
  daysOverdue: v.number(),
});

export type PriorityReminder = v.InferOutput<typeof PriorityReminderSchema>;

export const DashboardResponseSchema = v.object({
  weeklySummary: v.string(), // "2 souls won, 1 baptized this week"
  priorityReminders: v.array(PriorityReminderSchema), // Max 2 urgent items
  recentUpdates: v.array(ProspectListItemSchema), // Max 4 recent prospects
});

export type DashboardResponse = v.InferOutput<typeof DashboardResponseSchema>;

// Request schemas for mutations
export const CreateProspectRequestSchema = v.object({
  firstName: v.string(),
  nickName: v.optional(v.string()),
  lastName: v.string(),
  contactInfo: v.optional(v.string()),
  address: v.optional(v.string()),
  notes: v.optional(v.string()),
  status: v.optional(ProspectStatusSchema), // Defaults to "PROSPECT"
});

export type CreateProspectRequest = v.InferOutput<typeof CreateProspectRequestSchema>;

export const UpdateProspectRequestSchema = v.object({
  firstName: v.optional(v.string()),
  nickName: v.optional(v.string()),
  lastName: v.optional(v.string()),
  contactInfo: v.optional(v.string()),
  address: v.optional(v.string()),
  notes: v.optional(v.string()),
});

export type UpdateProspectRequest = v.InferOutput<typeof UpdateProspectRequestSchema>;

export const UpdateStatusRequestSchema = v.object({
  status: ProspectStatusSchema,
});

export type UpdateStatusRequest = v.InferOutput<typeof UpdateStatusRequestSchema>;

// Statistics schema (for future analytics)
export const ProspectStatsSchema = v.object({
  totalProspects: v.number(),
  weeklyStats: v.object({
    newProspects: v.number(),
    saved: v.number(),
    baptized: v.number(),
    foundationStarted: v.number(),
    ministryActivated: v.number(),
  }),
  monthlyStats: v.object({
    newProspects: v.number(),
    saved: v.number(),
    baptized: v.number(),
    foundationStarted: v.number(),
    ministryActivated: v.number(),
  }),
});

export type ProspectStats = v.InferOutput<typeof ProspectStatsSchema>;

export const ProspectStatsResponseSchema = ProspectStatsSchema;
export type ProspectStatsResponse = v.InferOutput<typeof ProspectStatsResponseSchema>;

// Search response (same as list but with query context)
export const SearchProspectsResponseSchema = ProspectListResponseSchema;
export type SearchProspectsResponse = v.InferOutput<typeof SearchProspectsResponseSchema>;
