// Common API types and base structures

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Base response types
export interface ApiSuccessResponse<T = any> {
  data: T;
  success: true;
}

export interface ApiErrorResponse {
  error: {
    type: string;
    code: string;
    message: string;
    request_id?: string;
    timestamp?: string;
  };
  success: false;
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

// Common image structure matching Go backend
export interface Image {
  url: string;
  altText?: string;
  blurHash: string;
  description?: string;
}

// Request/Response metadata
export interface RequestMetadata {
  requestId?: string;
  timestamp?: string;
  userAgent?: string;
}

// Type guards
export const isApiSuccess = <T>(response: any): response is ApiSuccessResponse<T> => {
  return response && response.success === true && 'data' in response;
};

export const isApiError = (response: any): response is ApiErrorResponse => {
  return response && response.success === false && 'error' in response;
};
