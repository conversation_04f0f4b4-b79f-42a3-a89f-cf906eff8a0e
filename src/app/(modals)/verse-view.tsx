import { useMemo, useRef, useState } from 'react';

import { Dimensions, Platform, Pressable, ScrollView, StyleSheet, View } from 'react-native';

import * as Haptics from 'expo-haptics';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams } from 'expo-router';

import PagerView from 'react-native-pager-view';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ChevronLeft, ChevronRight, Smartphone } from 'lucide-react-native';
import { MotiView } from 'moti';

import { BibleReference } from '@/api/schemas/guides';
import { CloseButton } from '@/components/CloseButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { dark } from '@/libs/unistyles/theme';

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export default function VerseView() {
  const { references, initialIndex } = useLocalSearchParams<{
    references: string;
    initialIndex?: string;
  }>();

  const verses = useMemo(() => {
    if (!references) return [];
    try {
      return JSON.parse(references);
    } catch (e) {
      console.error('Failed to parse references:', e);
      return [];
    }
  }, [references]);

  const [isLandscape, setIsLandscape] = useState(true);

  const { styles } = useStyles(getStylesheet(isLandscape));

  const [currentIndex, setCurrentIndex] = useState(initialIndex ? parseInt(initialIndex, 10) : 0);
  const pagerRef = useRef<PagerView>(null);

  // Get screen dimensions
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  const landscapeWidth = screenHeight;
  const landscapeHeight = screenWidth;

  // Reusable animation configurations
  const getRotationAnimation = (isLandscape: boolean, rotation: string) => ({
    transform: [
      {
        rotate: isLandscape ? rotation : '0deg',
      },
    ],
  });

  const getRotateButtonAnimation = (isLandscape: boolean) => ({
    transform: [
      {
        rotate: isLandscape ? '-90deg' : '90deg',
      },
    ],
  });

  const getContainerAnimation = (isLandscape: boolean) => ({
    width: isLandscape ? landscapeWidth : screenWidth,
    height: isLandscape ? landscapeHeight : screenHeight,
    transform: [
      {
        translateX: isLandscape ? -(landscapeWidth - screenWidth) / 2 : 0,
      },
      {
        translateY: isLandscape ? (screenHeight - landscapeHeight) / 2 : 0,
      },
      {
        rotate: isLandscape ? '-90deg' : '0deg',
      },
    ],
  });

  const springTransition = {
    type: 'spring' as const,
    damping: 20,
    stiffness: 90,
  };

  const timingTransition = {
    type: 'timing' as const,
    duration: 400,
  };

  const onPageSelected = (e: { nativeEvent: { position: number } }) => {
    setCurrentIndex(e.nativeEvent.position);
  };

  const goToPage = (index: number) => {
    if (pagerRef.current) {
      if (Platform.OS === 'ios') {
        pagerRef.current.setPage(index);
      } else {
        pagerRef.current.setPageWithoutAnimation(index);
      }
      setCurrentIndex(index);
    }
  };

  return (
    <ThemedView style={[styles.container, { width: screenWidth, height: screenHeight }]}>
      {/* Rotated background container */}
      <MotiView
        style={StyleSheet.absoluteFillObject}
        animate={getContainerAnimation(isLandscape)}
        transition={{
          ...timingTransition,
          rotate: springTransition,
        }}
      >
        <Image
          source="https://picsum.photos/2560/1400"
          style={{
            width: Math.max(screenWidth, screenHeight) * 1.2,
            height: Math.max(screenWidth, screenHeight) * 1.2,
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: [
              { translateX: -Math.max(screenWidth, screenHeight) * 0.6 },
              { translateY: -Math.max(screenWidth, screenHeight) * 0.6 },
            ],
          }}
          placeholder={{ blurhash }}
          contentFit="cover"
        />

        <LinearGradient
          colors={
            isLandscape
              ? ['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.3)']
              : ['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']
          }
          style={{
            position: 'absolute',
            width: Math.max(screenWidth, screenHeight) * 1.2,
            height: Math.max(screenWidth, screenHeight) * 1.2,
            top: '50%',
            left: '50%',
            transform: [
              { translateX: -Math.max(screenWidth, screenHeight) * 0.6 },
              { translateY: -Math.max(screenWidth, screenHeight) * 0.6 },
            ],
          }}
        />
      </MotiView>

      {/* Header buttons */}
      <View style={styles.headerButtons}>
        {/* Rotate button */}
        <MotiView animate={getRotateButtonAnimation(isLandscape)} transition={springTransition}>
          <Pressable
            onPress={() => {
              if (process.env.EXPO_OS === 'ios') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              setIsLandscape(!isLandscape);
            }}
            style={({ pressed }) => [styles.rotateButton, pressed && styles.buttonPressed]}
            hitSlop={8}
          >
            {isLandscape ? (
              <Smartphone size={20} color="white" strokeWidth={2.5} />
            ) : (
              <Smartphone size={20} color="white" strokeWidth={2.5} />
            )}
          </Pressable>
        </MotiView>

        {/* Close button */}
        <MotiView
          animate={getRotationAnimation(isLandscape, '-90deg')}
          transition={springTransition}
        >
          <CloseButton light />
        </MotiView>
      </View>

      {/* Rotated content container */}
      <MotiView
        animate={getContainerAnimation(isLandscape)}
        transition={{
          ...timingTransition,
          rotate: springTransition,
        }}
      >
        <View style={styles.safeAreaContainer}>
          <PagerView
            ref={pagerRef}
            style={styles.pagerView}
            initialPage={parseInt(initialIndex || '0', 10)}
            onPageSelected={onPageSelected}
            orientation="horizontal"
          >
            {verses.map((verse: BibleReference) => (
              <ThemedView key={verse.reference} style={styles.pageContainer}>
                <View style={styles.contentWrapper}>
                  <View style={styles.scrollContainer}>
                    {/* Add this container */}
                    <ScrollView
                      showsVerticalScrollIndicator={false}
                      contentContainerStyle={styles.scrollContentContainer}
                    >
                      <View style={styles.versesContainer}>
                        {verse.verses.map((v) => (
                          <View key={v.verse} style={styles.verseContainer}>
                            {verse.verses.length > 1 && (
                              <ThemedText style={styles.verseNumber}>{v.verse}</ThemedText>
                            )}
                            <ThemedText
                              style={[
                                styles.text,
                                verse.verses.length > 1 && styles.textMultipleVerses,
                              ]}
                            >
                              {v.text}
                            </ThemedText>
                          </View>
                        ))}
                      </View>
                      <ThemedView style={styles.referenceContainer}>
                        <ThemedText style={styles.reference}>{verse.reference}</ThemedText>
                      </ThemedView>
                    </ScrollView>
                  </View>
                </View>
              </ThemedView>
            ))}
          </PagerView>
        </View>
      </MotiView>

      {/* Navigation controls */}
      <View style={styles.navigationControls}>
        <ThemedView style={styles.navigation}>
          {/* Previous button */}
          <MotiView
            animate={getRotationAnimation(isLandscape, '-90deg')}
            transition={springTransition}
          >
            <ThemedView style={[styles.navButton, currentIndex === 0 && styles.navButtonDisabled]}>
              <Pressable
                onPress={() => currentIndex > 0 && goToPage(currentIndex - 1)}
                style={({ pressed }) => [styles.navPressable, pressed && styles.buttonPressed]}
                hitSlop={8}
                disabled={currentIndex === 0}
              >
                <ChevronLeft
                  size={24}
                  color={
                    currentIndex === 0
                      ? styles.disabledNavIconColor.color
                      : styles.navIconColor.color
                  }
                />
              </Pressable>
            </ThemedView>
          </MotiView>

          {/* Page indicator */}
          <MotiView
            animate={getRotationAnimation(isLandscape, '-90deg')}
            transition={springTransition}
          >
            <ThemedText style={styles.pageIndicator}>
              {currentIndex + 1} / {verses.length}
            </ThemedText>
          </MotiView>

          {/* Next button */}
          <MotiView
            animate={getRotationAnimation(isLandscape, '-90deg')}
            transition={springTransition}
          >
            <ThemedView
              style={[
                styles.navButton,
                currentIndex === verses.length - 1 && styles.navButtonDisabled,
              ]}
            >
              <Pressable
                onPress={() => currentIndex < verses.length - 1 && goToPage(currentIndex + 1)}
                style={({ pressed }) => [styles.navPressable, pressed && styles.buttonPressed]}
                hitSlop={8}
                disabled={currentIndex === verses.length - 1}
              >
                <ChevronRight
                  size={24}
                  color={
                    currentIndex === verses.length - 1
                      ? styles.disabledNavIconColor.color
                      : styles.navIconColor.color
                  }
                />
              </Pressable>
            </ThemedView>
          </MotiView>
        </ThemedView>
      </View>
    </ThemedView>
  );
}

const getStylesheet = (isLandscape: boolean) =>
  createStyleSheet((theme, runtime) => ({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    safeAreaContainer: {
      flex: 1,
      ...(isLandscape
        ? {
            // paddingTop: theme.spacing.sm,
            // paddingBottom: theme.spacing.sm,
            // paddingLeft: runtime.insets.top,
            // paddingRight: runtime.insets.bottom,
            // right: 0,
            // left: 0,
            ...Platform.select({
              ios: {
                paddingLeft: runtime.insets.bottom + theme.spacing.lg,
                paddingRight: runtime.insets.top,
              },
              android: {
                left: 0,
                // right: 0,
                paddingLeft: theme.spacing.lg,
                paddingRight: runtime.insets.top + theme.spacing['2xl'],
              },
            }),
          }
        : {
            // paddingTop: runtime.insets.top,
            // paddingBottom: runtime.insets.bottom,
            paddingLeft: theme.spacing.lg,
            paddingRight: theme.spacing.lg,
          }),
    },
    pagerView: {
      flex: 1,
    },
    pageContainer: {
      flex: 1,
      ...(isLandscape
        ? {
            marginTop: theme.spacing['2xl'],
          }
        : {
            marginTop: 60,
          }),
      marginBottom: 60,
      // backgroundColor: 'transparent',
      // justifyContent: 'center', // Center content vertically
    },
    contentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      ...(isLandscape
        ? {
            // paddingRight: runtime.insets.top,
            // paddingLeft: runtime.insets.bottom,
          }
        : {
            paddingRight: theme.spacing.md,
            paddingLeft: theme.spacing.md,
          }),
    },
    content: {
      flex: 1,
      // justifyContent: 'center',
      // alignItems: 'center',
      // backgroundColor: 'transparent',
      // paddingHorizontal: isLandscape ? theme.spacing.xl : undefined,
    },
    scrollContainer: {
      flex: 1,
      width: '100%',
    },
    scrollContentContainer: {
      flexGrow: 1,
      justifyContent: 'center',
    },
    versesContainer: {
      width: '100%',
    },
    verseContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.md,
      // paddingHorizontal: theme.spacing.md,
      width: '100%',
    },
    text: {
      flex: 1,
      fontSize: 32,
      fontFamily: theme.fonts.primary.semiBold,
      textAlign: 'center',
      lineHeight: 48,
      color: 'white',
    },
    textMultipleVerses: {
      textAlign: 'left',
      paddingLeft: theme.spacing.md, // Add a little padding after the verse number
    },
    verseNumber: {
      fontSize: 16,
      fontFamily: theme.fonts.primary.semiBold,
      color: 'white',
      marginTop: 8, // Adjust this to align with the first line of text
    },
    referenceContainer: {
      marginTop: theme.spacing.xl,
      alignItems: 'center',
    },
    reference: {
      fontSize: 18,
      fontFamily: theme.fonts.primary.semiBold,
      color: 'white',
    },
    navigationControls: {
      position: 'absolute',
      zIndex: 10,
      ...(isLandscape
        ? {
            right: theme.spacing.lg,
            height: '100%',
          }
        : {
            // Portrait mode
            bottom: 0,
            // left: 0,
            // right: 0,
            width: '100%',
          }),
    },
    navigation: {
      // backgroundColor: 'transparent',
      ...(isLandscape
        ? {
            // Landscape mode - stack controls vertically
            flex: 1,
            flexDirection: 'column-reverse', // This puts prev (left) arrow at bottom
            paddingTop: runtime.insets.top,
            paddingBottom: runtime.insets.bottom,
          }
        : {
            // Portrait mode
            flexDirection: 'row',
            paddingHorizontal: theme.spacing.lg,
            paddingBottom: runtime.insets.bottom,
          }),
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    navPressable: {
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
    buttonPressed: {
      opacity: 0.7,
      transform: [{ scale: 0.97 }],
    },
    navButton: {
      borderRadius: theme.radius.full,
      backgroundColor: theme.colors.background.secondary,
    },
    navButtonDisabled: {
      opacity: 0.5,
    },
    pageIndicator: {
      fontSize: 14,
      color: dark.colors.text.secondary,
    },
    navIconColor: {
      color: theme.colors.text.primary,
    },
    disabledNavIconColor: {
      color: theme.colors.text.tertiary,
    },
    headerButtons: {
      position: 'absolute',
      ...(isLandscape
        ? {
            // In landscape mode, position buttons along the left edge
            top: runtime.insets.top,
            bottom: runtime.insets.bottom,
            left: theme.spacing.lg,
            flexDirection: 'column-reverse', // Stack buttons vertically
            justifyContent: 'space-between',
          }
        : {
            // In portrait mode, position buttons along the top
            top: runtime.insets.top,
            left: theme.spacing.lg,
            right: theme.spacing.lg,
            flexDirection: 'row',
            justifyContent: 'space-between',
          }),
      alignItems: 'center',
      zIndex: 10,
    },
    rotateButton: {
      width: 40,
      height: 40,
      borderRadius: theme.radius.full,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.35)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
      ...Platform.select({
        ios: {
          overflow: 'visible',
          shadowColor: 'black',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.35,
          shadowRadius: 4,
        },
      }),
    },
  }));
