import React from 'react';

import { UITabsLayout } from '@/components/custom-tabs';
import { tabRoutes } from '@/navigation/routes';

// Convert each tab route to include required routeName property
const routesWithIcons = tabRoutes.map((route) => ({
  ...route,
  routeName: route.routeName || route.name, // Fallback to name if routeName is missing
}));

export default function TabLayout() {
  return <UITabsLayout routes={routesWithIcons} />;
}
