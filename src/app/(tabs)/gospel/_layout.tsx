import { Stack } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

export default function GospelLayout() {
  const { styles } = useStyles(stylesheet);
  const { isAuthenticated } = { isAuthenticated: true };

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="index"
        options={{
          headerTitle: 'Gospel',
          headerShown: true,
          headerLargeTitle: true,
          headerLargeTitleStyle: {
            ...styles.headerTitleStyle,
          },
        }}
      />

      {/* Guide Screens */}
      <Stack.Screen name="guide/section/[id]" />
      <Stack.Screen name="guide/step/[id]" />

      {/* Prospect Screens */}
      <Stack.Screen
        name="prospects/[id]"
        options={{
          headerTitle: 'Prospect Details',
          headerShown: true,
        }}
      />

      {isAuthenticated && (
        <Stack.Screen
          name="prospects/edit"
          options={{
            headerTitle: 'Edit Prospect',
          }}
        />
      )}

      {isAuthenticated && (
        <Stack.Screen
          name="prospects/new"
          options={{
            headerTitle: 'New Prospect',
            headerShown: true,
          }}
        />
      )}
    </Stack>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  headerTitleStyle: {
    fontFamily: theme.fonts.primary.semiBold,
  },
}));
