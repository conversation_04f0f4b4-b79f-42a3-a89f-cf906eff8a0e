import { useLocalSearchParams } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function EditProspect() {
  const { id } = useLocalSearchParams<{ id: string }>();

  return (
    <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ThemedText style={{ fontSize: 17, fontWeight: 'bold' }}>Edit Prospect</ThemedText>
      <ThemedText>Edit functionality will be implemented in the next phase</ThemedText>
      <ThemedText>Prospect ID: {id}</ThemedText>
    </ThemedView>
  );
}
