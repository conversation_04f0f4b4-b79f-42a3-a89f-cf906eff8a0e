import { ActivityIndicator, Button, Platform, View } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { HelloWave } from '@/components/HelloWave';
import ImageParallaxScrollView from '@/components/ImageParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/hooks/auth/useAuth';

export default function HomeScreen() {
  const { styles } = useStyles(stylesheet);
  const { login, register, logout, isLoading, isAuthenticated, userProfile, error } = useAuth();

  const handleSignIn = async () => {
    try {
      await login();
    } catch (error) {
      console.log('Sign in failed:', error);
    }
  };

  const handleSignUp = async () => {
    try {
      await register();
    } catch (error) {
      console.log('Sign up failed:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.log('Logout failed:', error);
    }
  };

  return (
    <ImageParallaxScrollView headerImageUri="https://picsum.photos/800/400">
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">
          {isAuthenticated && userProfile?.first_name
            ? `Welcome, ${userProfile.first_name}!`
            : 'Welcome!'}
        </ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 1: Try it</ThemedText>
        <ThemedText>
          Edit <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> to see changes.
          Press{' '}
          <ThemedText type="defaultSemiBold">
            {Platform.select({
              ios: 'cmd + d',
              android: 'cmd + m',
              web: 'F12',
            })}
          </ThemedText>{' '}
          to open developer tools.
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 2: Explore</ThemedText>
        <ThemedText>
          Tap the Explore tab to learn more about what's included in this starter app.
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
        <ThemedText>
          When you're ready, run{' '}
          <ThemedText type="defaultSemiBold">npm run reset-project</ThemedText> to get a fresh{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> directory. This will move the current{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> to{' '}
          <ThemedText type="defaultSemiBold">app-example</ThemedText>.
        </ThemedText>
      </ThemedView>

      <View style={styles.authContainer}>
        {error && <ThemedText style={styles.errorText}>{error}</ThemedText>}

        {isAuthenticated ? (
          <View style={styles.buttonContainer}>
            <Button
              title={isLoading ? 'Loading...' : 'Sign Out'}
              onPress={handleLogout}
              disabled={isLoading}
            />
          </View>
        ) : (
          <>
            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? 'Loading...' : 'Sign In'}
                onPress={handleSignIn}
                disabled={isLoading}
              />
            </View>
            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? 'Loading...' : 'Sign Up'}
                color="#000"
                onPress={handleSignUp}
                disabled={isLoading}
              />
            </View>
          </>
        )}

        {isLoading && <ActivityIndicator style={styles.loader} />}
      </View>
    </ImageParallaxScrollView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  authContainer: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    marginBottom: 8,
  },
  loader: {
    marginTop: 8,
  },
  errorText: {
    color: 'red',
    marginBottom: 8,
    textAlign: 'center',
  },
}));
