import { useEffect } from 'react';

import { Linking } from 'react-native';

import { Stack, router } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';

import { SystemBars } from 'react-native-edge-to-edge';
import 'react-native-gesture-handler';
import 'react-native-reanimated';

import { ThemeProvider } from '@react-navigation/native';

import { useMMKVDevTools } from '@dev-plugins/react-native-mmkv';
import {
  SourceSans3_400Regular,
  SourceSans3_400Regular_Italic,
  SourceSans3_600SemiBold,
  SourceSans3_700Bold,
  useFonts,
} from '@expo-google-fonts/source-sans-3';
import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';

import { API_CONFIG } from '@/api/client/config';
import { queryPersister } from '@/api/client/persistence';
import { useColorScheme } from '@/hooks/theme/useColorScheme';
import '@/libs/unistyles';
import {
  darkNavigationTheme,
  lightNavigationTheme,
} from '@/libs/unistyles/adapters/reactNavigation';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Offline-first configuration
      networkMode: 'offlineFirst',
      // Default cache settings from API config
      staleTime: API_CONFIG.cache.staleTime,
      gcTime: API_CONFIG.cache.cacheTime, // 24 hours for standard persistence
      refetchOnReconnect: API_CONFIG.cache.refetchOnReconnect,
      refetchOnWindowFocus: API_CONFIG.cache.refetchOnWindowFocus,
    },
    mutations: {
      networkMode: 'offlineFirst',
    },
  },
});

export default function RootLayout() {
  process.env.NODE_ENV === 'development' && useMMKVDevTools();

  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SourceSans3_400Regular,
    SourceSans3_400Regular_Italic,
    SourceSans3_600SemiBold,
    SourceSans3_700Bold,
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  useEffect(() => {
    const handleDeepLink = (event: { url: string }) => {
      if (event.url.includes('/callback')) {
        // Handle auth callback by redirecting to home
        router.replace('/(tabs)');
      }
    };

    // Set up deep link listener
    const subscription = Linking.addEventListener('url', handleDeepLink);

    return () => {
      subscription.remove();
    };
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister: queryPersister,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours for standard persistence
      }}
    >
      <ThemeProvider value={colorScheme === 'dark' ? darkNavigationTheme : lightNavigationTheme}>
        <Stack
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="(modals)" options={{ presentation: 'fullScreenModal' }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <SystemBars style="auto" />
      </ThemeProvider>
    </PersistQueryClientProvider>
  );
}
