import React from 'react';

import { Pressable } from 'react-native';

import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';

import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

import { ChevronLeft } from 'lucide-react-native';

import { ThemedView } from '@/components/ThemedView';

interface BackButtonProps {
  onPress?: () => void;
  hitSlop?: number;
  light?: boolean;
}

export function BackButton({ onPress, hitSlop = 8, light }: BackButtonProps) {
  const { styles } = useStyles(stylesheet);
  const router = useRouter();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.back();
    }
  };

  return (
    <ThemedView style={styles.backButtonContainer}>
      <BlurView
        tint={
          light
            ? 'systemUltraThinMaterialDark'
            : UnistylesRuntime.colorScheme === 'light'
              ? 'systemChromeMaterial'
              : 'systemMaterialDark'
        }
        style={[styles.backButton, light ? styles.backButtonLight : styles.backButtonSolid]}
      >
        <Pressable
          onPress={handlePress}
          style={({ pressed }) => [styles.pressable, pressed && styles.backButtonPressed]}
          hitSlop={hitSlop}
        >
          <ChevronLeft
            size={24}
            color={light ? styles.lightIconColor.color : styles.darkIconColor.color}
            strokeWidth={2.5}
          />
        </Pressable>
      </BlurView>
    </ThemedView>
  );
}

const stylesheet = createStyleSheet((theme, runtime) => ({
  backButtonContainer: {
    position: 'absolute',
    top: runtime.insets.top,
    left: theme.spacing.md,
    zIndex: 10,
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: theme.radius.full,
    overflow: 'hidden',
  },
  pressable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonSolid: {
    backgroundColor:
      UnistylesRuntime.colorScheme === 'light'
        ? `${theme.colors.background.secondary}80`
        : 'rgba(255, 255, 255, 0.15)',
    boxShadow: `0 1 2 rgba(0, 0, 0, 0.25)`,
  },
  backButtonLight: {
    backgroundColor:
      UnistylesRuntime.colorScheme === 'light'
        ? 'rgba(0, 0, 0, 0.35)'
        : 'rgba(255, 255, 255, 0.15)',
    boxShadow: `0 1 2 rgba(0, 0, 0, 0.25)`,
  },
  backButtonPressed: {
    opacity: 0.7,
    transform: [{ scale: 0.97 }],
  },
  lightIconColor: {
    color: 'white',
  },
  darkIconColor: {
    color: theme.colors.text.primary,
  },
}));
