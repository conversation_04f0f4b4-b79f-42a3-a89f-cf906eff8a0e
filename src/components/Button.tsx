import { forwardRef } from 'react';

import {
  ActivityIndicator,
  Platform,
  Pressable,
  PressableProps,
  StyleProp,
  View,
  ViewStyle,
} from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedText } from './ThemedText';

interface ButtonProps extends Omit<PressableProps, 'style'> {
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

export const Button = forwardRef<View, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      disabled = false,
      leftIcon,
      rightIcon,
      style,
      ...props
    },
    ref,
  ) => {
    const { styles } = useStyles(stylesheet);
    const isDisabled = disabled || loading;

    const getVariantStyles = () => {
      switch (variant) {
        case 'secondary':
          return styles.secondaryButton;
        case 'outline':
          return styles.outlineButton;
        case 'ghost':
          return styles.ghostButton;
        default:
          return styles.primaryButton;
      }
    };

    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return styles.smallButton;
        case 'lg':
          return styles.largeButton;
        default:
          return styles.mediumButton;
      }
    };

    const getTextStyles = () => {
      switch (variant) {
        case 'secondary':
          return styles.secondaryText;
        case 'outline':
        case 'ghost':
          return styles.outlineText;
        default:
          return styles.primaryText;
      }
    };

    const renderContent = () => {
      if (loading) {
        return (
          <ActivityIndicator
            color={
              variant === 'primary'
                ? styles.primaryLoaderColor.color
                : styles.secondaryLoaderColor.color
            }
          />
        );
      }

      return (
        <View style={styles.contentContainer}>
          {leftIcon && <View style={styles.iconContainer}>{leftIcon}</View>}
          {typeof children === 'string' ? (
            <ThemedText style={[styles.text, getTextStyles()]}>{children}</ThemedText>
          ) : (
            children
          )}
          {rightIcon && <View style={styles.iconContainer}>{rightIcon}</View>}
        </View>
      );
    };

    return (
      <Pressable
        ref={ref}
        disabled={isDisabled}
        style={({ pressed }) => [
          styles.button,
          getVariantStyles(),
          getSizeStyles(),
          fullWidth && styles.fullWidth,
          isDisabled && styles.disabled,
          pressed && styles.pressed,
          style,
        ]}
        {...props}
      >
        {renderContent()}
      </Pressable>
    );
  },
);

const stylesheet = createStyleSheet((theme) => ({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.radius.md,
    ...Platform.select({
      ios: {
        shadowColor: theme.colors.shadow,
        shadowOffset: theme.shadows.shadowOffset,
        shadowOpacity: theme.shadows.shadowOpacity,
        shadowRadius: theme.shadows.shadowRadius,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginHorizontal: theme.spacing.xs,
  },
  // Variants
  primaryButton: {
    backgroundColor: theme.colors.semantic.primary.active,
  },
  secondaryButton: {
    backgroundColor: theme.colors.semantic.primary.bg,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.semantic.primary.border,
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  // Sizes
  smallButton: {
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.md,
    minHeight: 32,
  },
  mediumButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    minHeight: 44,
  },
  largeButton: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    minHeight: 52,
  },
  // States
  pressed: {
    opacity: 0.8,
  },
  disabled: {
    opacity: 0.5,
  },
  fullWidth: {
    width: '100%',
  },
  // Text styles
  text: {
    textAlign: 'center',
    fontFamily: theme.fonts.primary.semiBold,
  },
  primaryText: {
    color: theme.colors.text.inverse,
  },
  secondaryText: {
    color: theme.colors.text.primary,
  },
  outlineText: {
    color: theme.colors.semantic.primary.text,
  },
  // Loader colors
  primaryLoaderColor: {
    color: theme.colors.text.inverse,
  },
  secondaryLoaderColor: {
    color: theme.colors.text.primary,
  },
}));
