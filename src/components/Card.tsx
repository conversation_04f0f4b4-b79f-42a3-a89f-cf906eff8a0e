import React, { useState } from 'react';

import { Platform, Pressable, View } from 'react-native';

import { router } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ChevronRight, Key, Lightbulb, Pointer } from 'lucide-react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

import { VerseButton } from './VerseButton';

interface CardProps {
  title: string;
  aim?: string | null;
  hint?: string | null;
  goldenKey?: string | null;
  type?: 'verse' | 'default';
  reference?: string;
  stepNumber?: number;
  onPress?: () => void;
  bibleReferences?: {
    reference: string;
    verses: { verse: number; text: string }[];
  }[];
}

export function Card({
  title,
  aim,
  hint,
  goldenKey,
  type = 'default',
  reference,
  stepNumber,
  bibleReferences,
  onPress,
}: CardProps) {
  const { styles } = useStyles(stylesheet);
  const [isHintMultiline, setIsHintMultiline] = useState(false);

  // Check if there are Bible references
  const hasVerses = bibleReferences && bibleReferences.length > 0;

  const onHintTextLayout = (event: { nativeEvent: { lines: any[] } }) => {
    setIsHintMultiline(event.nativeEvent.lines.length > 1);
  };

  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => [styles.cardWrapper, getPressableStyle(pressed)]}
    >
      <View style={styles.labelContainer}>
        <View style={styles.stepNumberPill}>
          <ThemedText style={styles.stepNumberText}>{stepNumber}</ThemedText>
        </View>
        {goldenKey && (
          <View style={styles.goldenKeyPill}>
            <Key size={14} color={styles.keyIconColor.color} />
            <ThemedText style={styles.goldenKeyText} numberOfLines={1}>
              {goldenKey}
            </ThemedText>
          </View>
        )}
      </View>

      <ThemedView style={styles.card}>
        <View
          style={[
            styles.mainContent,
            hasVerses ? styles.mainContentWithVerse : styles.mainContentWithoutVerse,
          ]}
        >
          <View style={styles.titleContainer}>
            <ThemedText type="defaultSemiBold" style={styles.title}>
              {title}
            </ThemedText>
            <ChevronRight size={20} color={styles.chevronIconColor.color} />
          </View>

          {aim && (
            <View style={styles.aimContainer}>
              <Pointer size={18} color={styles.aimIconColor.color} style={styles.aimIcon} />
              <ThemedText style={styles.aim}>{aim}</ThemedText>
            </View>
          )}

          {hint && (
            <View style={styles.bottomSection}>
              <View style={styles.hintContainer}>
                <Lightbulb size={18} color={styles.hintIconColor.color} style={styles.hintIcon} />
                <ThemedText style={styles.hint}>{hint}</ThemedText>
              </View>
            </View>
          )}

          {hasVerses && (
            <VerseButton
              onPress={() =>
                router.push({
                  pathname: '/verse-view',
                  params: { references: JSON.stringify(bibleReferences) },
                })
              }
            />
          )}
        </View>
      </ThemedView>
    </Pressable>
  );
}

const getPressableStyle = (pressed: boolean) => ({
  transform: [{ scale: pressed ? 0.98 : 1 }],
});

const stylesheet = createStyleSheet((theme) => ({
  cardWrapper: {
    flex: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
    }),
  },
  labelContainer: {
    position: 'absolute',
    top: 0,
    left: 16,
    zIndex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    transform: [{ translateY: -12 }],
  },
  card: {
    flex: 1,
    paddingTop: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.background.primary,
    ...Platform.select({
      ios: {
        overflow: 'visible',
      },
      android: {
        overflow: 'hidden',
        elevation: 2,
      },
    }),
  },
  stepNumberPill: {
    backgroundColor: theme.colors.brand.primary.base,
    paddingHorizontal: 12,
    height: 28,
    borderRadius: 999,
    minWidth: 32,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  stepNumberText: {
    color: theme.colors.brand.primary.subtle,
    fontSize: 16,
    fontFamily: theme.typography.h3.fontFamily,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  goldenKeyPill: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: theme.colors.semantic.action.bg,
    paddingHorizontal: 12,
    height: 28,
    borderRadius: 999,
    borderWidth: 1,
    borderColor: theme.colors.semantic.action.border,
  },
  goldenKeyText: {
    color: theme.colors.semantic.action.text,
    fontSize: theme.typography.subtitle1.fontSize,
    fontWeight: '500',
    maxWidth: 200,
    textAlignVertical: 'center',
  },
  mainContent: {
    flex: 1,
    gap: 8,
    paddingHorizontal: 16,
  },
  mainContentWithVerse: {
    paddingBottom: 48,
  },
  mainContentWithoutVerse: {
    paddingBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    flex: 1,
  },
  aimContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  aimIcon: {
    marginTop: 2,
  },
  aim: {
    flex: 1,
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.fonts.primary.italic,
    color: theme.colors.brand.primary.base,
  },
  bottomSection: {
    flex: 1,
  },
  hintContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  hintIcon: {
    marginTop: 2,
  },
  hint: {
    flex: 1,
    fontSize: theme.typography.body2.fontSize,
    color: theme.colors.text.subdued,
  },
  // Icon colors
  keyIconColor: {
    color: theme.colors.semantic.action.active,
  },
  chevronIconColor: {
    color: theme.colors.text.secondary,
  },
  aimIconColor: {
    color: theme.colors.brand.primary.base,
  },
  hintIconColor: {
    color: theme.colors.text.subdued,
  },
}));
