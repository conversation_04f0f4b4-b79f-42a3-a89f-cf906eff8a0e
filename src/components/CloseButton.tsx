import React from 'react';

import { Platform, Pressable } from 'react-native';

import { useRouter } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { X } from 'lucide-react-native';

import { ThemedView } from '@/components/ThemedView';

interface CloseButtonProps {
  onPress?: () => void;
  hitSlop?: number;
  light?: boolean;
  position?: 'left' | 'right';
}

export function CloseButton({
  onPress,
  hitSlop = 8,
  light = true,
  position = 'right',
}: CloseButtonProps) {
  const { styles } = useStyles(stylesheet);
  const router = useRouter();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.back();
    }
  };

  return (
    <ThemedView style={styles.closeButtonContainer}>
      <Pressable
        onPress={handlePress}
        style={({ pressed }) => [
          styles.closeButton,
          light ? styles.closeButtonLight : styles.closeButtonSolid,
          pressed && styles.closeButtonPressed,
        ]}
        hitSlop={hitSlop}
      >
        <X
          size={24}
          color={light ? styles.lightIconColor.color : styles.darkIconColor.color}
          strokeWidth={2.5}
        />
      </Pressable>
    </ThemedView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  closeButtonContainer: {
    backgroundColor: 'transparent',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: theme.radius.full,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonSolid: {
    backgroundColor: theme.colors.background.primary,
    ...Platform.select({
      ios: {
        overflow: 'visible',
        shadowColor: 'black',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
      android: {
        overflow: 'hidden',
        elevation: 3,
      },
    }),
  },
  closeButtonLight: {
    backgroundColor: 'rgba(0, 0, 0, 0.35)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    ...Platform.select({
      ios: {
        overflow: 'visible',
        shadowColor: 'black',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.35,
        shadowRadius: 4,
      },
    }),
  },
  closeButtonPressed: {
    opacity: 0.7,
    transform: [{ scale: 0.97 }],
  },
  lightIconColor: {
    color: 'white',
  },
  darkIconColor: {
    color: theme.colors.text.primary,
  },
}));
