import React, { useCallback, useMemo, useState } from 'react';

import { TextStyle, TouchableOpacity, View } from 'react-native';

import * as Haptics from 'expo-haptics';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ChevronsDown } from 'lucide-react-native';
import { MotiView } from 'moti';

import { ThemedText } from './ThemedText';

interface CollapsibleTextProps {
  text: string;
  numberOfLines?: number;
  style?: TextStyle;
  showToggleButton?: boolean;
}

export const CollapsibleText = React.memo(function CollapsibleText({
  text,
  numberOfLines,
  style,
  showToggleButton = false,
}: CollapsibleTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [fullHeight, setFullHeight] = useState(0);
  const [collapsedHeight, setCollapsedHeight] = useState(0);
  const { styles } = useStyles(stylesheet);

  // Memoize combined styles
  const combinedStyle = useMemo(
    () => ({
      ...styles.toggleText,
      fontFamily: style?.fontFamily,
      fontSize: style?.fontSize,
      lineHeight: style?.lineHeight,
    }),
    [style?.fontFamily, style?.fontSize, style?.lineHeight, styles.toggleText],
  );

  // If no numberOfLines, render simple text
  if (!numberOfLines) {
    return (
      <View style={styles.container}>
        <ThemedText style={combinedStyle}>{text}</ThemedText>
      </View>
    );
  }

  // Memoize toggle handler
  const handleToggle = useCallback(() => {
    try {
      if (process.env.EXPO_OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      setIsExpanded((prev) => !prev);
    } catch (error) {
      setIsExpanded((prev) => !prev);
      console.log('Haptics error:', error);
    }
  }, []);

  // Memoize animation props
  const motiAnimateProps = useMemo(
    () => ({
      height: isExpanded ? fullHeight : collapsedHeight,
    }),
    [isExpanded, fullHeight, collapsedHeight],
  );

  const chevronAnimateProps = useMemo(
    () => ({
      rotate: isExpanded ? '180deg' : '0deg',
      opacity: 0.6,
    }),
    [isExpanded],
  );

  return (
    <View style={styles.container}>
      {/* Hidden measurement views */}
      <View
        style={styles.measureContainer}
        onLayout={useCallback(
          ({ nativeEvent }: { nativeEvent: { layout: { height: number } } }) => {
            setFullHeight(nativeEvent.layout.height);
          },
          [],
        )}
      >
        <ThemedText style={combinedStyle}>{text}</ThemedText>
      </View>

      <View
        style={styles.measureContainer}
        onLayout={useCallback(
          ({ nativeEvent }: { nativeEvent: { layout: { height: number } } }) => {
            setCollapsedHeight(nativeEvent.layout.height);
          },
          [],
        )}
      >
        <ThemedText numberOfLines={numberOfLines} style={combinedStyle}>
          {text}
        </ThemedText>
      </View>

      {/* Main content */}
      <TouchableOpacity onPress={handleToggle}>
        <MotiView
          animate={motiAnimateProps}
          transition={{
            type: 'spring',
            damping: 20,
            stiffness: 200,
          }}
          style={styles.animatedContainer}
        >
          <ThemedText numberOfLines={isExpanded ? undefined : numberOfLines} style={style}>
            {text}
          </ThemedText>
        </MotiView>

        {showToggleButton && (
          <MotiView
            animate={chevronAnimateProps}
            transition={{
              type: 'timing',
              duration: 200,
            }}
            style={styles.chevronContainer}
          >
            <ChevronsDown size={20} color={styles.chevronColor.color} />
          </MotiView>
        )}
      </TouchableOpacity>

      {/* Toggle button - only shown if showToggleButton is true */}
      {/* {showToggleButton && (
        <TouchableOpacity onPress={handleToggle} style={styles.toggleButton}>
          <ThemedText style={toggleStyle}>
            {isExpanded ? 'Show Less' : 'Show More'}
            <View style={styles.iconContainer}>
              {isExpanded ? (
                <ChevronUp
                  size={theme.typography.subtitle1.fontSize}
                  color={theme.colors.brand.primary.base}
                />
              ) : (
                <ChevronDown
                  size={theme.typography.subtitle1.fontSize}
                  color={theme.colors.brand.primary.base}
                />
              )}
            </View>
          </ThemedText>
        </TouchableOpacity>
      )} */}
    </View>
  );
});

// Move stylesheet outside component
const stylesheet = createStyleSheet((theme) => ({
  container: {
    width: '100%',
  },
  measureContainer: {
    position: 'absolute',
    opacity: 0,
    zIndex: -1,
  },
  animatedContainer: {
    overflow: 'hidden',
  },
  chevronContainer: {
    position: 'absolute',
    bottom: -theme.spacing.xl,
    alignSelf: 'center',
    padding: 4,
  },
  toggleText: {
    color: theme.colors.text.primary,
  },
  chevronColor: {
    color: theme.colors.text.disabled,
  },
}));
