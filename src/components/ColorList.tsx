import React from 'react';

import { ScrollView, View } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface ColorListProps {
  color: string;
}

const ColorList = ({ color }: ColorListProps) => {
  const { styles } = useStyles(stylesheet);

  return (
    <ScrollView>
      {[1, 0.8, 0.5, 0.2, 1, 0.8, 0.5, 0.2].map((opacity, index) => (
        <View
          key={`color-${index}-${opacity}`}
          style={[styles.color, { backgroundColor: color, opacity }]}
        />
      ))}
    </ScrollView>
  );
};

const stylesheet = createStyleSheet((theme, runtime) => ({
  color: {
    flex: 1,
    height: 150,
    borderRadius: theme.radius['2xl'],
    borderCurve: 'continuous',
    marginBottom: theme.spacing.md,
    marginHorizontal: theme.spacing.md,
  },
}));

export default ColorList;
