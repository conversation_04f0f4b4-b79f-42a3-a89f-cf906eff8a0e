import { useEffect } from 'react';

import Animated, {
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedText } from '@/components/ThemedText';

export function HelloWave() {
  const { styles } = useStyles(stylesheet);

  const rotationAnimation = useSharedValue(0);
  const triggerAnimation = useSharedValue(0);

  // Initial animation trigger
  useEffect(() => {
    triggerAnimation.value = 1;
  }, []);

  // Handle animation with useAnimatedReaction
  useAnimatedReaction(
    () => triggerAnimation.value,
    (trigger) => {
      if (trigger === 1) {
        rotationAnimation.value = withRepeat(
          withSequence(withTiming(25, { duration: 150 }), withTiming(0, { duration: 150 })),
          4,
          false, // don't reverse
          () => {
            // Reset trigger when animation is done
            triggerAnimation.value = 0;
          },
        );
      }
    },
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotationAnimation.value}deg` }],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <ThemedText style={styles.text}>👋</ThemedText>
    </Animated.View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  text: {
    fontSize: 28,
    lineHeight: 32,
    marginTop: -6,
  },
}));
