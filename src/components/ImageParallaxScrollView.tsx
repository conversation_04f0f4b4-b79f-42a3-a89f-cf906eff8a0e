import type { PropsWithChildren } from 'react';

import { StyleSheet } from 'react-native';

import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';

import Animated, {
  interpolate,
  useAnimatedReaction,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
  useSharedValue,
} from 'react-native-reanimated';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedView } from '@/components/ThemedView';
import { useTabBarHeight } from '@/hooks/ui/useTabbarHeight';

const HEADER_HEIGHT = 250;
export { HEADER_HEIGHT };

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

type Props = PropsWithChildren<{
  headerImageUri: string;
  headerImageBlurHash?: string;
  headerContent?: React.ReactNode;
  floatingContent?: React.ReactNode;
}>;

export default function ImageParallaxScrollView({
  children,
  headerImageUri,
  headerImageBlurHash,
  headerContent,
  floatingContent,
}: Props) {
  const tabBarHeight = useTabBarHeight();
  const { styles } = useStyles(stylesheet);
  const scrollRef = useAnimatedRef<Animated.ScrollView>();
  const scrollOffset = useScrollViewOffset(scrollRef);
  const headerTransform = useSharedValue({ translateY: 0, scale: 1 });

  // Add this to handle scroll offset changes more efficiently
  useAnimatedReaction(
    () => scrollOffset.value,
    (currentOffset) => {
      headerTransform.value = {
        translateY: interpolate(
          currentOffset,
          [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
          [-HEADER_HEIGHT / 2, 0, HEADER_HEIGHT * 0.75],
        ),
        scale: interpolate(currentOffset, [-HEADER_HEIGHT, 0, HEADER_HEIGHT], [2, 1, 1]),
      };
    },
  );

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: headerTransform.value.translateY },
        { scale: headerTransform.value.scale },
      ],
    };
  });

  const AnimatedImage = Animated.createAnimatedComponent(Image);

  return (
    <ThemedView style={styles.container}>
      {floatingContent && (
        <Animated.View style={[styles.floatingContent]}>{floatingContent}</Animated.View>
      )}
      <Animated.ScrollView
        ref={scrollRef}
        scrollIndicatorInsets={{ bottom: tabBarHeight }}
        contentContainerStyle={{ paddingBottom: tabBarHeight }}
      >
        <Animated.View style={[styles.header, headerAnimatedStyle]}>
          <AnimatedImage
            source={{ uri: headerImageUri }}
            style={styles.headerImage}
            placeholder={{ blurhash: headerImageBlurHash || blurhash }}
            contentFit="cover"
          />
          <LinearGradient
            colors={['rgba(0, 0, 0, 0.2)', 'rgba(0, 0, 0, 0.7)']}
            locations={[0.2, 0.8]}
            style={styles.headerGradient}
          />
          {headerContent && (
            <ThemedView style={[styles.headerOverlay, { paddingTop: UnistylesRuntime.insets.top }]}>
              {headerContent}
            </ThemedView>
          )}
        </Animated.View>
        <ThemedView style={styles.content}>{children}</ThemedView>
      </Animated.ScrollView>
    </ThemedView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  floatingContent: {
    position: 'absolute',
    zIndex: 1,
    width: '100%',
  },
  header: {
    height: HEADER_HEIGHT,
    overflow: 'hidden',
  },
  headerImage: {
    width: '100%',
    height: '100%',
    // resizeMode: "cover",
  },
  headerGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
    padding: theme.spacing.lg,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
    gap: theme.spacing.md,
    overflow: 'hidden',
    paddingBottom: rt.insets.bottom,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20, // Creates an overlap with the header image
  },
}));
