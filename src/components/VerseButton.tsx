import { Platform, Pressable } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { Maximize2 } from 'lucide-react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

export const VerseButton = ({ onPress }: { onPress: () => void }) => {
  const { styles, theme } = useStyles(stylesheet);

  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => [styles.verseButtonContainer, pressed && styles.verseButtonPressed]}
    >
      <ThemedView style={styles.verseButton}>
        <Maximize2 size={16} color={theme.colors.brand.primary.emphasis} />
        <ThemedText style={styles.verseButtonText}>Show Verse</ThemedText>
      </ThemedView>
    </Pressable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  cardWrapper: {
    flex: 1,
    ...Platform.select({
      ios: {
        shadowColor: theme.colors.shadow,
        shadowOffset: theme.shadows.shadowOffset,
        shadowOpacity: theme.shadows.shadowOpacity,
        shadowRadius: theme.shadows.shadowRadius,
      },
    }),
    // Add this for smooth animation
    transform: [{ scale: 1 }],
    opacity: 1,
  },

  verseButtonContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    // Add this for smooth animation
    transform: [{ scale: 1 }],
    opacity: 1,
  },

  verseButtonPressed: {
    opacity: theme.colors.opacity,
  },

  verseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: theme.typography.subtitle2.fontSize,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.brand.primary.subtle,
    ...Platform.select({
      ios: {
        borderTopLeftRadius: 12,
        borderBottomRightRadius: 12,
      },
      android: {
        borderTopLeftRadius: 12,
      },
    }),
  },

  verseButtonText: {
    fontFamily: theme.typography.body2.fontFamily,
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.colors.brand.primary.emphasis,
  },

  // Add ripple effect for Android
  android_ripple: {
    color: theme.colors.brand.primary.subtle,
    borderless: true,
  },
}));
