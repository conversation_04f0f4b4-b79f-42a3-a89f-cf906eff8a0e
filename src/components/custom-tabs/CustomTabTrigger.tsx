import React from 'react';

import { Pressable, ViewStyle } from 'react-native';

import { useTabTrigger } from 'expo-router/ui';

import Animated, {
  interpolate,
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { icons } from '../ui/TabIcons';

interface CustomTabTriggerProps {
  name: string;
  label: string;
  routeName: keyof typeof icons;
  style?: ViewStyle;
}

export function CustomTabTrigger({ name, label, routeName, style }: CustomTabTriggerProps) {
  // Use the hook to get the current state of this tab
  const { trigger, triggerProps } = useTabTrigger({ name });
  const isFocused = trigger?.isFocused || false;

  // Get styles from stylesheet
  const { styles } = useStyles(stylesheet);

  const scale = useSharedValue(0);

  useAnimatedReaction(
    () => isFocused,
    (currentFocused) => {
      const targetValue =
        typeof currentFocused === 'boolean' ? (currentFocused ? 1 : 0) : currentFocused;
      scale.value = withSpring(targetValue, { duration: 350 });
    },
    [isFocused],
  );

  const animatedStyle = useAnimatedStyle(() => {
    const scaleValue = interpolate(scale.value, [0, 1], [1, 1.4]);
    const topValue = interpolate(scale.value, [0, 1], [0, 8]);
    return { transform: [{ scale: scaleValue }], top: topValue };
  });

  const animatedTextStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scale.value, [0, 1], [1, 0]);
    return {
      transform: [{ scale: opacity }],
    };
  });

  return (
    <Pressable {...triggerProps} style={[style]}>
      <Animated.View style={animatedStyle}>
        {icons[routeName]({
          color: isFocused ? styles.focusedTextStyle.color : styles.unfocusedTextStyle.color,
          size: 24,
        })}
      </Animated.View>
      <Animated.Text
        style={[
          styles.text,
          isFocused ? styles.focusedTextStyle : styles.unfocusedTextStyle,
          animatedTextStyle,
        ]}
      >
        {label}
      </Animated.Text>
    </Pressable>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  text: {
    fontFamily: theme.typography.subtitle2.fontFamily,
    fontSize: theme.typography.subtitle2.fontSize,
    lineHeight: theme.typography.subtitle2.lineHeight,
  },
  focusedTextStyle: {
    color: theme.colors.brand.primary.base,
  },
  unfocusedTextStyle: {
    color: theme.colors.text.subdued,
  },
}));
