import React, { useEffect } from 'react';

import { Platform } from 'react-native';

import { BlurView } from 'expo-blur';
import { Href } from 'expo-router';
import { TabList, TabSlot, TabTrigger, Tabs } from 'expo-router/ui';

import Animated, {
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

import { useAtomValue } from 'jotai';

import { CustomTabTrigger } from '@/components/custom-tabs/CustomTabTrigger';
import { useTabBarMeasure } from '@/hooks/ui/useTabbarHeight';
import { isTabBarVisibleAtom } from '@/state/atoms/ui';

import { icons } from '../ui/TabIcons';

export interface UITabsLayoutProps {
  routes: {
    name: string;
    href: Href;
    label: string;
    routeName: keyof typeof icons;
  }[];
}

export function UITabsLayout({ routes }: UITabsLayoutProps) {
  const { styles } = useStyles(stylesheet);
  const { onLayout } = useTabBarMeasure();
  const isVisible = useAtomValue(isTabBarVisibleAtom);
  const opacity = useSharedValue(isVisible ? 1 : 0);
  const translateY = useSharedValue(isVisible ? 0 : 100);

  // React to changes in visibility
  useAnimatedReaction(
    () => isVisible,
    (currentVisible) => {
      opacity.value = withTiming(currentVisible ? 1 : 0, { duration: 300 });
      translateY.value = withTiming(currentVisible ? 0 : 100, { duration: 300 });
    },
    [isVisible],
  );

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateY: translateY.value }],
  }));

  // This component and structure is required for using the expo-router/ui
  // First we need to define the screens with TabTrigger inside TabList
  // Then we render our custom UI below
  return (
    <Tabs>
      {/* This is required to render the actual screen content */}
      <TabSlot />

      {/* These TabTriggers define the available routes, but we hide them */}
      <TabList style={{ display: 'none' }}>
        {routes.map((route) => (
          <TabTrigger key={route.name} name={route.name} href={route.href} />
        ))}
      </TabList>

      {/* Our custom tab bar UI */}
      <Animated.View style={[styles.container, !isVisible && styles.hidden, animatedStyle]}>
        <BlurView
          onLayout={onLayout}
          tint={Platform.select({
            android:
              UnistylesRuntime.colorScheme === 'light'
                ? 'systemChromeMaterial'
                : 'systemThickMaterialDark',
            ios: 'systemChromeMaterial',
          })}
          intensity={100}
          style={styles.tabbar}
        >
          {routes.map((route) => (
            <CustomTabTrigger
              key={route.name}
              name={route.name}
              label={route.label}
              routeName={route.routeName}
              style={styles.tabbarItem}
            />
          ))}
        </BlurView>
      </Animated.View>
    </Tabs>
  );
}

const stylesheet = createStyleSheet((theme, runtime) => ({
  container: {
    position: 'absolute',
    left: theme.spacing.lg,
    right: theme.spacing.lg,
    bottom: 0,
  },
  hidden: {
    pointerEvents: 'none',
  },
  tabbar: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderRadius: theme.radius['2xl'],
    borderCurve: 'continuous',
    overflow: 'hidden',
    bottom: runtime.insets.bottom,
    boxShadow: `0 1 2 rgba(0, 0, 0, 0.25)`,
    ...Platform.select({
      android: {
        backgroundColor:
          UnistylesRuntime.colorScheme === 'light' ? theme.colors.background.secondary : '#AEAEB2',
      },
    }),
  },
  tabbarItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
}));
