import { forwardRef, useState } from 'react';

import { TextInput, TextInputProps, View } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedText } from '../ThemedText';

const RequiredAsterisk = ({ style }: { style?: any }) => (
  <ThemedText style={[{ color: '#ef4444', fontWeight: 'bold' }, style]}> *</ThemedText>
);

interface FormInputProps extends TextInputProps {
  label: string;
  error?: string;
  required?: boolean;
}

export const FormInput = forwardRef<TextInput, FormInputProps>(
  ({ label, error, required, ...props }, ref) => {
    const { styles } = useStyles(stylesheet);
    const [isFocused, setIsFocused] = useState(false);

    return (
      <View style={styles.container}>
        <View style={styles.labelContainer}>
          <ThemedText style={styles.label}>
            {label.replace(' *', '')}
            {required && <RequiredAsterisk />}
          </ThemedText>
        </View>
        <TextInput
          ref={ref}
          style={[
            styles.input,
            error && styles.inputError,
            isFocused && styles.inputFocused,
            props.multiline && styles.multilineInput,
          ]}
          placeholderTextColor={styles.placeholderColor.color}
          accessible={true}
          accessibilityLabel={label.replace(' *', '')}
          accessibilityHint={error ? `${label.replace(' *', '')}, ${error}` : undefined}
          accessibilityRole="text"
          accessibilityState={{
            disabled: props.editable === false,
            selected: isFocused,
          }}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          {...props}
        />
        {error && <ThemedText style={styles.errorText}>{error}</ThemedText>}
      </View>
    );
  },
);

const stylesheet = createStyleSheet((theme) => ({
  container: {
    gap: theme.spacing.xs,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    ...theme.typography.label,
    color: theme.colors.text.primary,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  input: {
    height: 52,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radius.lg,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.background.primary,
    color: theme.colors.text.primary,
    ...theme.typography.body1,
  },
  inputError: {
    borderColor: theme.colors.semantic.error.border,
  },
  inputFocused: {
    borderColor: theme.colors.semantic.primary.border,
    borderWidth: 2,
  },
  multilineInput: {
    height: 'auto',
    minHeight: 80,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    textAlignVertical: 'top',
  },
  errorText: {
    ...theme.typography.subtitle1,
    color: theme.colors.semantic.error.text,
  },
  placeholderColor: {
    color: theme.colors.text.tertiary,
  },
}));
