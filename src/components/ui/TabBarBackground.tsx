// This is a shim for web and Android where the tab bar is generally opaque.
import React from 'react';

import { StyleSheet, View } from 'react-native';

export function TabBarBackground() {
  return <View style={styles.background} />;
}

export function useBottomTabOverflow() {
  return 0;
}

const styles = StyleSheet.create({
  background: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
});

export default TabBarBackground;
