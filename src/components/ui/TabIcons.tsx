import { ComponentProps } from 'react';

import { AntDesign, Feather } from '@expo/vector-icons';

type AntDesignProps = ComponentProps<typeof AntDesign>;
type FeatherProps = ComponentProps<typeof Feather>;

type IconProps = {
  size?: number;
  color?: string;
  style?: AntDesignProps['style'] | FeatherProps['style'];
};

export const icons: Record<string, (props: IconProps) => JSX.Element> = {
  home: (props: IconProps) => <AntDesign name="home" {...props} />,
  gospel: (props: IconProps) => <Feather name="book-open" {...props} />,
  lessons: (props: IconProps) => <AntDesign name="book" {...props} />,
  devotional: (props: IconProps) => <Feather name="bookmark" {...props} />,
  support: (props: IconProps) => <Feather name="gift" {...props} />,
};

// Main export
export const TabIcons = { icons };
