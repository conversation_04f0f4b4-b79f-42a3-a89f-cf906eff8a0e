import { View, type ViewProps } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedView({ style, lightColor, darkColor, ...otherProps }: ThemedViewProps) {
  const { styles } = useStyles(stylesheet);
  return <View style={[styles.defaultBackground, style]} {...otherProps} />;
}

const stylesheet = createStyleSheet(() => ({
  defaultBackground: {
    backgroundColor: 'transparent',
  },
}));
