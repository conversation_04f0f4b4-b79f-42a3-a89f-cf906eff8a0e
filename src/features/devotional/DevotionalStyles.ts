import { createStyleSheet } from 'react-native-unistyles';

const DevotionalStyles = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  contentContainer: {
    gap: 16,
    marginBottom: 16,
  },
  card: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  cardTitle: {
    marginBottom: 8,
  },
  cardContent: {
    marginBottom: theme.spacing.sm,
  },
  dateText: {
    color: theme.colors.text.secondary,
    fontSize: 14,
  },
  authorText: {
    color: theme.colors.text.secondary,
    fontSize: 14,
    fontStyle: 'italic',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  verseContainer: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  verseText: {
    fontStyle: 'italic',
    fontSize: 14,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  searchInput: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 8,
    padding: 8,
    marginBottom: 16,
  },
}));

export default DevotionalStyles;
