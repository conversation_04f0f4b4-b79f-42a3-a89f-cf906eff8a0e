import { api } from '@/api';
import { StorageKeys } from '@/constants/StorageKeys';

export interface DevotionalEntry {
  id: string;
  title: string;
  content: string;
  date: string;
  author?: string;
  imageUrl?: string;
  verses?: string[];
}

// Fetch all devotional entries
export async function getDevotionalEntries(): Promise<DevotionalEntry[]> {
  try {
    const response = await api.get('/devotional/entries');
    return response.data;
  } catch (error) {
    console.error('Error fetching devotional entries:', error);
    return [];
  }
}

// Fetch a single devotional entry by ID
export async function getDevotionalEntry(id: string): Promise<DevotionalEntry | null> {
  try {
    const response = await api.get(`/devotional/entries/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching devotional entry ${id}:`, error);
    return null;
  }
}

// Store devotional entries locally
export function storeDevotionalEntries(entries: DevotionalEntry[]): void {
  try {
    const storage = localStorage || window.localStorage;
    storage.setItem(StorageKeys.DEVOTIONAL.ENTRIES, JSON.stringify(entries));
  } catch (error) {
    console.error('Error storing devotional entries:', error);
  }
}

// Get locally stored devotional entries
export function getLocalDevotionalEntries(): DevotionalEntry[] {
  try {
    const storage = localStorage || window.localStorage;
    const entriesJson = storage.getItem(StorageKeys.DEVOTIONAL.ENTRIES);
    return entriesJson ? JSON.parse(entriesJson) : [];
  } catch (error) {
    console.error('Error retrieving local devotional entries:', error);
    return [];
  }
}
