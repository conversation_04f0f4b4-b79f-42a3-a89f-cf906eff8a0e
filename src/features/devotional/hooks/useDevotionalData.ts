import { useCallback, useEffect, useState } from 'react';

import { getDevotionalEntries, getLocalDevotionalEntries, storeDevotionalEntries } from '../api';
import { DevotionalEntry, DevotionalFilter } from '../types';

export function useDevotionalData(filter?: DevotionalFilter) {
  const [entries, setEntries] = useState<DevotionalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEntries = useCallback(async () => {
    setIsLoading(true);
    try {
      // Try to get from API first
      const apiEntries = await getDevotionalEntries();

      if (apiEntries.length > 0) {
        setEntries(apiEntries);
        storeDevotionalEntries(apiEntries);
      } else {
        // Fall back to local storage if API fails or returns empty
        const localEntries = getLocalDevotionalEntries();
        setEntries(localEntries);
      }

      setError(null);
    } catch (err) {
      setError('Failed to fetch devotional entries');
      console.error(err);

      // Fall back to local storage on error
      const localEntries = getLocalDevotionalEntries();
      setEntries(localEntries);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEntries();
  }, [fetchEntries]);

  // Apply filters if provided
  const filteredEntries = useCallback(() => {
    if (!filter) return entries;

    return entries.filter((entry) => {
      const entryDate = new Date(entry.date);

      // Filter by date range
      if (filter.startDate && entryDate < filter.startDate) return false;
      if (filter.endDate && entryDate > filter.endDate) return false;

      // Filter by author
      if (filter.author && entry.author !== filter.author) return false;

      // Filter by search term
      if (filter.searchTerm) {
        const term = filter.searchTerm.toLowerCase();
        const matchesTitle = entry.title.toLowerCase().includes(term);
        const matchesContent = entry.content.toLowerCase().includes(term);
        if (!matchesTitle && !matchesContent) return false;
      }

      return true;
    });
  }, [entries, filter]);

  return {
    entries: filter ? filteredEntries() : entries,
    isLoading,
    error,
    refresh: fetchEntries,
  };
}
