// Type definitions for the Devotional feature

export interface DevotionalEntry {
  id: string;
  title: string;
  content: string;
  date: string;
  author?: string;
  imageUrl?: string;
  verses?: string[];
  isFavorite?: boolean;
}

export interface DevotionalState {
  entries: DevotionalEntry[];
  selectedEntry: DevotionalEntry | null;
  isLoading: boolean;
  error: string | null;
}

export interface DevotionalFilter {
  startDate?: Date;
  endDate?: Date;
  author?: string;
  searchTerm?: string;
}

export interface DevotionalSettings {
  showFavoritesFirst: boolean;
  sortOrder: 'asc' | 'desc';
  notificationsEnabled: boolean;
  reminderTime?: string;
}
