import { createStyleSheet } from 'react-native-unistyles';

export const gospelStyles = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  scrollViewStyle: {
    backgroundColor: theme.colors.background.primary,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  // Common section styles
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  iconContainer: {
    padding: theme.spacing.sm,
    borderRadius: theme.radius.md,
    backgroundColor: theme.colors.brand.primary.subtle,
    marginRight: theme.radius.lg,
  },
  headerIcon: {
    size: theme.spacing.lg,
    color: theme.colors.brand.primary.base,
  },
  headerTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  sectionDescription: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.typography.body2.fontFamily,
    color: theme.colors.text.subdued,
    lineHeight: theme.typography.body2.lineHeight,
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
  },
}));
