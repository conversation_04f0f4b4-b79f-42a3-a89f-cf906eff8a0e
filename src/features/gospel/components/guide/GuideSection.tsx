import { Pressable } from 'react-native';

import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { Link } from 'expo-router';

import { useStyles } from 'react-native-unistyles';

import { NotebookText } from 'lucide-react-native';

import { type SectionSummary } from '@/api/schemas/guides';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { gospelStyles } from '@/features/gospel/GospelStyles';
import { guideStyles } from '@/features/gospel/components/guide/GuideStyles';

interface GuideSectionProps {
  sections: SectionSummary[];
}

export const GuideSection = ({ sections }: GuideSectionProps) => {
  const { styles: baseStyles } = useStyles(gospelStyles);
  const { styles } = useStyles(guideStyles);

  const getStepRange = (sectionNo: number) => {
    const ranges = {
      1: '1-3',
      2: '4-7',
      3: '8-11',
      4: '12-13',
    };
    return ranges[sectionNo as keyof typeof ranges];
  };

  return (
    <ThemedView style={[baseStyles.section, styles.container]}>
      <ThemedView style={baseStyles.sectionHeader}>
        <ThemedView style={baseStyles.iconContainer}>
          <NotebookText size={baseStyles.headerIcon.size} color={baseStyles.headerIcon.color} />
        </ThemedView>
        <ThemedView style={baseStyles.headerTextContainer}>
          <ThemedText style={baseStyles.sectionDescription}>
            Step-by-step soul-winning guide
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {sections.map((section) => (
        <Link
          key={section.sectionNo}
          href={{
            pathname: '/gospel/guide/section/[id]',
            params: { id: section.sectionNo, section: JSON.stringify(section) },
          }}
          asChild
        >
          <Pressable>
            {({ pressed }) => (
              <ThemedView style={[styles.cardWrapper, styles.card, pressed && styles.cardPressed]}>
                <Image
                  source={{ uri: section.image?.url }}
                  placeholder={{ blurhash: section.image?.blurHash }}
                  style={styles.backgroundImage}
                  contentFit="cover"
                />
                <LinearGradient
                  colors={['rgba(0, 0, 0, 0.8)', 'rgba(0, 0, 0, 0.6)', 'rgba(0, 0, 0, 0.0)']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  locations={[0, 0.55, 1]}
                  style={styles.overlay}
                >
                  <ThemedView style={styles.contentContainer}>
                    <ThemedView style={styles.sectionBadge}>
                      <ThemedText style={styles.sectionNumber}>
                        Steps {getStepRange(section.sectionNo)}
                      </ThemedText>
                    </ThemedView>
                    <ThemedText style={styles.title}>{section.title}</ThemedText>
                    <ThemedText style={styles.subtitle}>{section.subtitle}</ThemedText>
                  </ThemedView>
                </LinearGradient>
              </ThemedView>
            )}
          </Pressable>
        </Link>
      ))}
    </ThemedView>
  );
};
