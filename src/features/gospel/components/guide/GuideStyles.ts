import { createStyleSheet } from 'react-native-unistyles';

export const guideStyles = createStyleSheet((theme) => ({
  // Guide section container
  container: {
    marginTop: theme.spacing.sm,
    paddingTop: theme.spacing.md,
  },

  // Header icon styles
  headerIcon: {
    size: theme.spacing.lg,
    color: theme.colors.brand.primary.base,
  },

  // Guide card styles
  cardWrapper: {
    marginBottom: theme.spacing.md,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.15)',
  },
  card: {
    position: 'relative',
    flexDirection: 'row',
    borderRadius: theme.radius.xl,
    overflow: 'hidden',
    height: 140,
  },
  cardPressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  sectionBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.sm,
    alignSelf: 'flex-start',
    marginBottom: theme.spacing.sm,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionNumber: {
    color: 'white',
    fontSize: theme.typography.subtitle2.fontSize,
    fontFamily: theme.fonts.primary.regular,
  },
  contentContainer: {
    flex: 1,
    padding: theme.spacing.md,
    justifyContent: 'center',
  },
  title: {
    fontSize: theme.typography.h4.fontSize,
    fontFamily: theme.typography.h4.fontFamily,
    lineHeight: theme.typography.h4.lineHeight,
    color: 'white',
  },
  subtitle: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.typography.body2.fontFamily,
    lineHeight: theme.typography.body2.lineHeight,
    color: 'rgba(255, 255, 255, 0.8)',
  },
}));
