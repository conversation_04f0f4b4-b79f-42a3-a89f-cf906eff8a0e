import { createStyleSheet } from 'react-native-unistyles';

export const prospectStyles = createStyleSheet((theme) => ({
  // Prospect section container
  container: {
    marginBottom: theme.spacing.md,
  },

  // Recent prospects card
  recentProspectsCard: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.xl,
    overflow: 'hidden',
  },
  recentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  recentHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  addNewLink: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  addNewLinkPressed: {
    opacity: 0.7,
  },
  addNewText: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.typography.body2.fontFamily,
    color: theme.colors.brand.primary.interaction,
  },
  recentTitle: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.fonts.primary.semiBold,
    color: theme.colors.text.primary,
  },
  recentList: {
    padding: theme.spacing.sm,
  },

  // Individual prospect card styles
  prospectCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.radius.lg,
    paddingHorizontal: theme.radius.lg,
    borderRadius: theme.radius.lg,
  },
  cardPressed: {
    backgroundColor: theme.colors.brand.primary.accent,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.radius.lg,
  },
  dot: {
    width: theme.spacing.sm,
    height: theme.spacing.sm,
    borderRadius: theme.spacing.xs,
    backgroundColor: theme.colors.brand.primary.base,
  },
  prospectName: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.typography.body2.fontFamily,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.colors.text.primary,
  },
  prospectDate: {
    fontSize: theme.typography.subtitle2.fontSize,
    fontFamily: theme.typography.subtitle2.fontFamily,
    lineHeight: theme.typography.subtitle2.lineHeight,
    color: theme.colors.text.subdued,
  },
  statusBadge: {
    paddingVertical: theme.radius.md,
    paddingHorizontal: theme.radius.lg,
    borderRadius: theme.radius.full,
  },
  statusText: {
    fontSize: theme.typography.subtitle2.fontSize,
    fontFamily: theme.typography.subtitle2.fontFamily,
    lineHeight: theme.typography.subtitle2.lineHeight,
  },
}));
