import { Pressable } from 'react-native';

import { Link } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { Clock, Plus, Users2 } from 'lucide-react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { gospelStyles } from '@/features/gospel/GospelStyles';
import { prospectStyles } from '@/features/gospel/components/prospects/ProspectStyles';
import {
  ProspectStatus,
  RecentProspectCard,
} from '@/features/gospel/components/prospects/RecentProspectCard';

// Define interface for Prospect data
export interface RecentProspect {
  id: number;
  name: string;
  status: ProspectStatus;
  date: string;
}

interface ProspectsSectionProps {
  recentProspects: RecentProspect[];
}

export const ProspectsSection = ({ recentProspects }: ProspectsSectionProps) => {
  const { styles: baseStyles } = useStyles(gospelStyles);
  const { styles } = useStyles(prospectStyles);
  const { styles: sectionStyles } = useStyles(prospectSectionStyles);

  return (
    <ThemedView style={[baseStyles.section, styles.container]}>
      <ThemedView style={baseStyles.sectionHeader}>
        <ThemedView style={baseStyles.iconContainer}>
          <Users2 size={sectionStyles.iconSize.size} color={sectionStyles.iconSize.color} />
        </ThemedView>
        <ThemedView style={baseStyles.headerTextContainer}>
          <ThemedText style={baseStyles.sectionDescription}>
            Track prospects and spiritual journeys
          </ThemedText>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.recentProspectsCard}>
        <ThemedView style={styles.recentHeader}>
          <ThemedView style={styles.recentHeaderLeft}>
            <Clock size={sectionStyles.smallIcon.size} color={sectionStyles.smallIcon.color} />
            <ThemedText style={styles.recentTitle}>Recent Updates</ThemedText>
          </ThemedView>

          <Link href="/gospel/prospects/new" asChild>
            <Pressable>
              {({ pressed }) => (
                <ThemedView style={[styles.addNewLink, pressed && styles.addNewLinkPressed]}>
                  <Plus size={sectionStyles.smallIcon.size} color={sectionStyles.smallIcon.color} />
                  <ThemedText style={styles.addNewText}>Add New</ThemedText>
                </ThemedView>
              )}
            </Pressable>
          </Link>
        </ThemedView>

        <ThemedView style={styles.recentList}>
          {recentProspects.map((prospect) => (
            <Link key={prospect.id} href={`/gospel/prospects/${prospect.id}`} asChild>
              <RecentProspectCard
                name={prospect.name}
                status={prospect.status}
                date={prospect.date}
              />
            </Link>
          ))}
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
};

const prospectSectionStyles = createStyleSheet((theme) => ({
  // Icon styles - following pattern from SectionDetailScreen
  iconSize: {
    size: theme.spacing.lg,
    color: theme.colors.brand.primary.base,
  },
  smallIcon: {
    size: theme.spacing.md,
    color: theme.colors.brand.primary.interaction,
  },
}));
