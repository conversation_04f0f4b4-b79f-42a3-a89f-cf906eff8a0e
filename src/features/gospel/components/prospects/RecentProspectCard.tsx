import { Pressable } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export type ProspectStatus = 'Prospect' | 'Saved' | 'Baptized';

interface RecentProspectCardProps {
  name: string;
  status: ProspectStatus;
  date: string;
  onPress?: () => void;
}

export const RecentProspectCard = ({ name, status, date, onPress }: RecentProspectCardProps) => {
  const { styles } = useStyles(prospectCardStyles);

  return (
    <Pressable onPress={onPress}>
      {({ pressed }) => (
        <ThemedView style={[styles.prospectCard, pressed && styles.cardPressed]}>
          <ThemedView style={styles.leftSection}>
            <ThemedView style={styles.dot} />
            <ThemedView>
              <ThemedText style={styles.prospectName}>{name}</ThemedText>
              <ThemedText style={styles.prospectDate}>{date}</ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView
            style={[
              styles.statusBadge,
              status === 'Prospect' && styles.prospectBadge,
              status === 'Saved' && styles.savedBadge,
              status === 'Baptized' && styles.baptizedBadge,
            ]}
          >
            <ThemedText
              style={[
                styles.statusText,
                status === 'Prospect' && styles.prospectText,
                status === 'Saved' && styles.savedText,
                status === 'Baptized' && styles.baptizedText,
              ]}
            >
              {status}
            </ThemedText>
          </ThemedView>
        </ThemedView>
      )}
    </Pressable>
  );
};

const prospectCardStyles = createStyleSheet((theme) => ({
  // Base styles
  prospectCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.radius.lg,
    paddingHorizontal: theme.radius.lg,
    borderRadius: theme.radius.lg,
  },
  cardPressed: {
    backgroundColor: theme.colors.brand.primary.accent,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.radius.lg,
  },
  dot: {
    width: theme.spacing.sm,
    height: theme.spacing.sm,
    borderRadius: theme.spacing.xs,
    backgroundColor: theme.colors.brand.primary.base,
  },
  prospectName: {
    fontSize: theme.typography.body2.fontSize,
    fontFamily: theme.typography.body2.fontFamily,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.colors.text.primary,
  },
  prospectDate: {
    fontSize: theme.typography.subtitle2.fontSize,
    fontFamily: theme.typography.subtitle2.fontFamily,
    lineHeight: theme.typography.subtitle2.lineHeight,
    color: theme.colors.text.subdued,
  },
  statusBadge: {
    paddingVertical: theme.radius.md,
    paddingHorizontal: theme.radius.lg,
    borderRadius: theme.radius.full,
    borderWidth: 1,
  },
  statusText: {
    fontSize: theme.typography.subtitle2.fontSize,
    fontFamily: theme.typography.subtitle2.fontFamily,
    lineHeight: theme.typography.subtitle2.lineHeight,
  },

  // Status-specific styles
  prospectBadge: {
    backgroundColor: theme.colors.semantic.warning.bg,
    borderColor: theme.colors.semantic.warning.border,
  },
  prospectText: {
    color: theme.colors.semantic.warning.text,
  },
  savedBadge: {
    backgroundColor: theme.colors.brand.primary.subtle,
    borderColor: theme.colors.brand.primary.muted,
  },
  savedText: {
    color: theme.colors.brand.primary.emphasis,
  },
  baptizedBadge: {
    backgroundColor: theme.colors.semantic.success.bg,
    borderColor: theme.colors.semantic.success.border,
  },
  baptizedText: {
    color: theme.colors.semantic.success.text,
  },
}));
