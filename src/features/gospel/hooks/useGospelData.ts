// Simplified Gospel data hooks using new API infrastructure with Valibot validation
import { guides } from '@/api/endpoints';
import { CACHE_KEYS } from '@/api/hooks';
import { useValidatedGospelQuery, useValidatedQuery } from '@/api/hooks/useValidatedQuery';
import {
  type SectionDetail,
  SectionDetailSchema,
  type SectionsListResponse,
  SectionsListResponseSchema,
  type StepDetail,
  StepDetailSchema,
} from '@/api/schemas/guides';

// Hook for getting all sections with validation
export const useGospelSections = () => {
  return useValidatedGospelQuery(
    [CACHE_KEYS.guides.sections],
    () => guides.getSections(),
    SectionsListResponseSchema,
    {
      // Extract sections array from validated response
      select: (response) => response.sections,
    },
  );
};

// Hook for getting a specific section with steps and validation
export const useGospelSection = (id: string, enabled = true) => {
  return useValidatedGospelQuery(
    [CACHE_KEYS.guides.section(id)],
    () => guides.getSection(id),
    SectionDetailSchema,
    {
      enabled: !!id && enabled,
    },
  );
};

// Hook for getting a specific step with validation
export const useGospelStep = (id: string, enabled = true) => {
  return useValidatedGospelQuery(
    [CACHE_KEYS.guides.step(id)],
    () => guides.getStep(id),
    StepDetailSchema,
    {
      enabled: !!id && enabled,
    },
  );
};

// Convenience hooks for common patterns

// Get sections count
export const useGospelSectionsCount = () => {
  const { data } = useGospelSections();
  return data?.length || 0;
};

// Get section navigation info
export const useSectionNavigation = (currentSectionNo: number) => {
  const { data: sections } = useGospelSections();

  if (!sections) {
    return { hasPrevious: false, hasNext: false };
  }

  const maxSection = Math.max(...sections.map((s) => s.sectionNo));

  return {
    hasPrevious: currentSectionNo > 1,
    hasNext: currentSectionNo < maxSection,
    previousSectionNo: currentSectionNo > 1 ? currentSectionNo - 1 : null,
    nextSectionNo: currentSectionNo < maxSection ? currentSectionNo + 1 : null,
  };
};

// Get step navigation info within a section
export const useStepNavigation = (currentStepNo: number, sectionId: string) => {
  const { data: section } = useGospelSection(sectionId);

  if (!section?.steps) {
    return { hasPrevious: false, hasNext: false };
  }

  const maxStep = Math.max(...section.steps.map((s) => s.stepNo));

  return {
    hasPrevious: currentStepNo > 1,
    hasNext: currentStepNo < maxStep,
    previousStepNo: currentStepNo > 1 ? currentStepNo - 1 : null,
    nextStepNo: currentStepNo < maxStep ? currentStepNo + 1 : null,
  };
};
