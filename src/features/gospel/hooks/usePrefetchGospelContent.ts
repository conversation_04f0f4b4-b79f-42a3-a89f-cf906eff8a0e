import { useEffect } from 'react';

import { useQueryClient } from '@tanstack/react-query';
import * as v from 'valibot';

import { guides } from '@/api/endpoints';
import { CACHE_KEYS } from '@/api/hooks';
import { SectionDetailSchema, StepDetailSchema } from '@/api/schemas/guides';

import { useGospelSections } from './useGospelData';

export const usePrefetchGospelContent = () => {
  const queryClient = useQueryClient();
  const { data: sections } = useGospelSections();

  useEffect(() => {
    if (!sections?.length) return;

    const prefetchContent = async () => {
      try {
        // Prefetch all section details
        const sectionPromises = sections.map((section) =>
          queryClient.prefetchQuery({
            queryKey: [CACHE_KEYS.guides.section(section.sectionNo.toString())],
            queryFn: async () => {
              const data = await guides.getSection(section.sectionNo.toString());

              try {
                const validatedData = v.parse(SectionDetailSchema, data);
                return validatedData;
              } catch (error) {
                // Same error handling as existing hooks
                const validationError = error as v.ValiError<typeof SectionDetailSchema>;
                console.error('API Validation Error:', {
                  endpoint: `section:${section.sectionNo}`,
                  message: validationError.message,
                  issues: validationError.issues?.map((issue) => ({
                    path: issue.path?.map((p: any) => p.key).join('.'),
                    message: issue.message,
                    expected: issue.expected,
                    received: issue.received,
                  })),
                });
                throw new Error(
                  `API validation failed for section:${section.sectionNo}: ${validationError.message}`,
                );
              }
            },
          }),
        );

        // Wait for all sections to be prefetched first
        await Promise.allSettled(sectionPromises);

        // Now prefetch all step details from all sections
        const stepPromises: Promise<any>[] = [];

        sections.forEach((section) => {
          // We need to get the section data to access steps
          const sectionData = queryClient.getQueryData([
            CACHE_KEYS.guides.section(section.sectionNo.toString()),
          ]) as any;

          if (sectionData?.steps) {
            sectionData.steps.forEach((step: any) => {
              stepPromises.push(
                queryClient.prefetchQuery({
                  queryKey: [CACHE_KEYS.guides.step(step.stepNo.toString())],
                  queryFn: async () => {
                    const data = await guides.getStep(step.stepNo.toString());

                    try {
                      const validatedData = v.parse(StepDetailSchema, data);
                      return validatedData;
                    } catch (error) {
                      // Same error handling as existing hooks
                      const validationError = error as v.ValiError<typeof StepDetailSchema>;
                      console.error('API Validation Error:', {
                        endpoint: `step:${step.stepNo}`,
                        message: validationError.message,
                        issues: validationError.issues?.map((issue) => ({
                          path: issue.path?.map((p: any) => p.key).join('.'),
                          message: issue.message,
                          expected: issue.expected,
                          received: issue.received,
                        })),
                      });
                      throw new Error(
                        `API validation failed for step:${step.stepNo}: ${validationError.message}`,
                      );
                    }
                  },
                }),
              );
            });
          }
        });

        // Prefetch all steps
        await Promise.allSettled(stepPromises);

        console.log(
          `Prefetched ${sections.length} sections and ${stepPromises.length} steps for offline access`,
        );
      } catch (error) {
        console.warn('Gospel content prefetch error:', error);
      }
    };

    prefetchContent();
  }, [sections, queryClient]);
};
