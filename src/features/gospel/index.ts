// Main index file for Gospel feature
// Export public API for the Gospel feature

// Export screens
export { GospelScreen } from './screens/GospelScreen';
export { SectionDetailScreen } from './screens/SectionDetailScreen';
export { StepDetailScreen } from './screens/StepDetailScreen';

// Export styles
export * from './GospelStyles';

// Export API endpoints
export * from '@/api/endpoints/guides';

// Export hooks
export * from './hooks';

// Export types
export * from './types';

// Export components
// Explicitly export to avoid name conflicts
export { GuideSection, prospectStyles, ProspectsSection, RecentProspectCard } from './components';

// Note: Golden keys now handled by Go backend - no static constants needed
