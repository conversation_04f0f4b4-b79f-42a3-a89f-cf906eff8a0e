import { ActivityIndicator, ScrollView } from 'react-native';

import { useStyles } from 'react-native-unistyles';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { gospelStyles } from '@/features/gospel/GospelStyles';
import { GuideSection } from '@/features/gospel/components/guide/GuideSection';
import {
  ProspectsSection,
  RecentProspect,
} from '@/features/gospel/components/prospects/ProspectsSection';
import { useGospelSections, usePrefetchGospelContent } from '@/features/gospel/hooks';
import { useTabBarHeight } from '@/hooks/ui/useTabbarHeight';

export function GospelScreen() {
  const { styles } = useStyles(gospelStyles);
  const { data: sections, isLoading, error } = useGospelSections();

  usePrefetchGospelContent();

  const tabBarHeight = useTabBarHeight();

  const recentProspects: RecentProspect[] = [
    { id: 1, name: '<PERSON>', status: 'Saved', date: '2 hours ago' },
    { id: 2, name: '<PERSON>', status: 'Prospect', date: 'Yesterday' },
    { id: 3, name: '<PERSON>', status: 'Baptized', date: '2 days ago' },
  ];

  if (isLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator />
      </ThemedView>
    );
  }

  if (error || !sections) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Failed to load sections</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView
      style={styles.scrollViewStyle}
      contentContainerStyle={[styles.scrollContent, { paddingBottom: tabBarHeight }]}
      contentInsetAdjustmentBehavior="automatic"
      scrollIndicatorInsets={{ bottom: tabBarHeight }}
    >
      <ThemedView style={styles.container}>
        <ProspectsSection recentProspects={recentProspects} />
        <GuideSection sections={sections} />
      </ThemedView>
    </ScrollView>
  );
}
