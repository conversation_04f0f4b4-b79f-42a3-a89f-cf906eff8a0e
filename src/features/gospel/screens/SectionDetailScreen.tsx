import { useMemo } from 'react';

import { ActivityIndicator, Platform } from 'react-native';

import { useLocalSearchParams, useRouter } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { ScrollText } from 'lucide-react-native';

import { type Step } from '@/api/schemas/guides';
import { BackButton } from '@/components/BackButton';
import { Card } from '@/components/Card';
import { CollapsibleText } from '@/components/CollapsibleText';
import ImageParallaxScrollView from '@/components/ImageParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useGospelSection } from '@/features/gospel/hooks';

export function SectionDetailScreen() {
  const { styles } = useStyles(stylesheet);
  const router = useRouter();

  const { id, section: sectionParam } = useLocalSearchParams<{
    id: string;
    section: string;
  }>();

  // Parse the passed section data
  const passedSection = useMemo(() => {
    try {
      return JSON.parse(sectionParam);
    } catch {
      return null;
    }
  }, [sectionParam]);

  const { data: fullSection, isLoading } = useGospelSection(id);

  // Combine the passed data with the fetched data
  const section = useMemo(
    () => ({
      ...passedSection,
      ...fullSection, // This will override with the full data when available
    }),
    [passedSection, fullSection],
  );

  // Show loading only if we don't have the passed data
  if (isLoading && !passedSection) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator />
      </ThemedView>
    );
  }

  if (!section) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Failed to load section</ThemedText>
      </ThemedView>
    );
  }

  return (
    <>
      {/* Floating Back Button */}
      <BackButton light />

      <ImageParallaxScrollView
        headerImageUri={section.image?.url}
        headerImageBlurHash={section.image?.blurHash}
        headerContent={
          <ThemedView style={[styles.headerContent, { backgroundColor: 'transparent' }]}>
            <ThemedView style={styles.textContainer}>
              <ThemedText style={styles.sectionNumber}>Section {section.sectionNo}</ThemedText>
              <ThemedView style={styles.titleGroup}>
                <ThemedText type="displaySmall" style={styles.sectionTitle}>
                  {section.title}
                </ThemedText>
                <ThemedText type="titleMedium" style={styles.sectionSubtitle}>
                  {section.subtitle}
                </ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        }
      >
        {/* Description Section */}
        <ThemedView style={styles.descriptionContainer}>
          <ThemedView style={styles.overviewHeader}>
            <ThemedView style={styles.iconContainer}>
              <ScrollText size={styles.sectionIcon.size} color={styles.sectionIcon.color} />
            </ThemedView>
            <ThemedView style={styles.headerContent}>
              <ThemedText type="defaultSemiBold">Section Overview</ThemedText>
              <ThemedText style={styles.hint}>{section.hint}</ThemedText>
            </ThemedView>
          </ThemedView>

          <CollapsibleText
            text={section.overview}
            numberOfLines={2}
            showToggleButton={true}
            style={styles.description}
          />
        </ThemedView>

        {/* Steps List */}
        {isLoading ? (
          <ThemedView style={styles.stepsContainer}>
            <ActivityIndicator />
          </ThemedView>
        ) : section.steps && section.steps.length > 0 ? (
          <ThemedView style={styles.stepsContainer}>
            {section.steps.map((step: Step) => (
              <Card
                key={step.stepNo}
                title={` ${step.title}`}
                aim={step.aim}
                hint={step.hint}
                stepNumber={step.stepNo}
                goldenKey={step.goldenKey?.key}
                bibleReferences={step.bibleReferences}
                onPress={() =>
                  router.push({
                    pathname: '/gospel/guide/step/[id]',
                    params: { id: step.stepNo, step: JSON.stringify(step) },
                  })
                }
              />
            ))}
          </ThemedView>
        ) : null}
      </ImageParallaxScrollView>
    </>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  sectionIcon: {
    size: theme.spacing.lg,
    color: theme.colors.brand.primary.base,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  backButtonContainer: {
    position: 'absolute',
    top: rt.insets.top,
    left: theme.spacing.md,
    zIndex: 10,
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: theme.radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.35)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)', // Subtle border
    ...Platform.select({
      ios: {
        overflow: 'visible',
        shadowColor: 'black',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
    }),
  },
  backButtonPressed: {
    backgroundColor: 'rgba(0, 0, 0, 0.45)',
    transform: [{ scale: 0.97 }],
  },
  textContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
  },
  titleGroup: {
    backgroundColor: 'transparent',
    gap: 1,
    marginTop: theme.spacing.xs,
  },
  sectionNumber: {
    color: 'white',
    fontFamily: theme.fonts.primary.regular,
    fontSize: theme.typography.subtitle2.fontSize,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    opacity: 0.9,
  },
  sectionTitle: {
    color: 'white',
    fontFamily: theme.fonts.primary.bold,
    fontSize: theme.typography.h2.fontSize,
    lineHeight: theme.typography.h2.lineHeight * 0.9,
    letterSpacing: -0.3,
  },
  sectionSubtitle: {
    color: 'white',
    fontFamily: theme.fonts.primary.regular,
    fontSize: theme.typography.h4.fontSize,
    lineHeight: theme.typography.h4.lineHeight,
    opacity: 0.9,
  },
  descriptionContainer: {
    paddingTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    backgroundColor: theme.colors.background.primary,
    gap: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  description: {
    fontFamily: theme.typography.body2.fontFamily,
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
  },
  hint: {
    fontFamily: theme.fonts.primary.regular,
    fontSize: theme.typography.subtitle1.fontSize,
    lineHeight: theme.typography.subtitle1.lineHeight,
    color: theme.colors.text.subdued,
  },
  stepsContainer: {
    flex: 1,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.secondary,
    gap: theme.spacing['2xl'],
  },
  goldenKey: {
    color: theme.colors.brand.primary,
    fontWeight: 'bold',
  },
  overviewHeader: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  iconContainer: {
    width: theme.spacing['2xl'],
    height: theme.spacing['2xl'],
    backgroundColor: theme.colors.brand.primary.subtle,
    borderRadius: theme.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stickyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  sectionHeader: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.background.primary,
  },
}));
