import React, { useEffect, useMemo } from 'react';

import { ActivityIndicator, Platform, Pressable, ScrollView } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';

import Markdown from 'react-native-markdown-display';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { useSetAtom } from 'jotai';
import { BookOpen, Key, Lightbulb, Maximize2, Pointer } from 'lucide-react-native';

import { BackButton } from '@/components/BackButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useGospelStep } from '@/features/gospel/hooks';
import { isTabBarVisibleAtom } from '@/state/atoms/ui';

export function StepDetailScreen() {
  const { styles } = useStyles(stylesheet);
  const setIsTabBarVisible = useSetAtom(isTabBarVisibleAtom);

  useEffect(() => {
    // Hide tab bar when component mounts
    setIsTabBarVisible(false);

    // Show tab bar when component unmounts
    return () => {
      setIsTabBarVisible(true);
    };
  }, [setIsTabBarVisible]);

  const { id, step: stepParam } = useLocalSearchParams<{
    id: string;
    step: string;
  }>();

  // Parse the passed step data
  const passedStep = useMemo(() => {
    try {
      return JSON.parse(stepParam);
    } catch {
      return null;
    }
  }, [stepParam]);

  // Use the new Gospel hook
  const { data: fullStep, isLoading } = useGospelStep(id);

  // Combine the passed data with the fetched data
  const step = useMemo(
    () => ({
      ...passedStep,
      ...fullStep, // This will override with the full data when available
    }),
    [passedStep, fullStep],
  );

  // Show loading only if we don't have the passed data
  if (isLoading && !passedStep) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator />
      </ThemedView>
    );
  }

  if (!step) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Failed to load step</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Floating Back Button */}
      <BackButton />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="subtitle1" style={styles.stepNumber}>
            STEP {step.stepNo}
          </ThemedText>
          <ThemedText style={styles.stepTitle}>{step.title}</ThemedText>
        </ThemedView>

        {/* Bible References */}
        {step.bibleReferences && step.bibleReferences.length > 0 && (
          <ThemedView style={styles.bibleRefsContainer}>
            {step.bibleReferences.map((ref: any, index: number) => (
              <Pressable
                key={ref.reference}
                style={({ pressed }) => [
                  styles.bibleRefButton,
                  pressed && styles.bibleRefButtonPressed,
                ]}
                onPress={() =>
                  router.push({
                    pathname: '/verse-view',
                    params: {
                      references: JSON.stringify(step.bibleReferences),
                      initialIndex: index.toString(),
                    },
                  })
                }
              >
                <BookOpen size={styles.bibleRefIcon.size} color={styles.bibleRefIcon.color} />
                <ThemedText type="defaultSemiBold" style={styles.bibleRefText}>
                  {ref.reference}
                </ThemedText>
                {/* <Maximize2 size={styles.maximizeIcon.size} color={styles.maximizeIcon.color} /> */}
              </Pressable>
            ))}
          </ThemedView>
        )}

        {/* Key Point */}
        <ThemedView style={styles.infoSection}>
          <ThemedView style={styles.infoRow}>
            <ThemedView style={[styles.iconContainer, styles.keyPointIcon]}>
              <Pointer size={styles.keyPointIcon.iconSize} color={styles.keyPointIcon.iconColor} />
            </ThemedView>
            <ThemedView style={styles.infoContent}>
              <ThemedText type="defaultSemiBold">Key Point</ThemedText>
              <ThemedText style={styles.infoText}>{step.aim}</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Main Content */}
        {isLoading ? (
          <ThemedView style={styles.mainContent}>
            <ActivityIndicator />
          </ThemedView>
        ) : step.content ? (
          <ThemedView style={styles.mainContent}>
            <Markdown
              style={{
                body: styles.bodyText,
                blockquote: {
                  backgroundColor: styles.md_blockquote.backgroundColor,
                  borderColor: styles.md_blockquote.borderColor,
                },
                code_inline: {
                  fontFamily: styles.md_code_inline.fontFamily,
                  backgroundColor: styles.md_code_inline.backgroundColor,
                  color: styles.md_code_inline.color,
                },
                strong: {
                  fontFamily: styles.md_strong.fontFamily,
                  fontSize: styles.md_strong.fontSize,
                  lineHeight: styles.md_strong.lineHeight,
                },
                em: {
                  fontFamily: styles.md_em.fontFamily,
                  fontSize: styles.md_em.fontSize,
                  lineHeight: styles.md_em.lineHeight,
                },
              }}
            >
              {step.content}
            </Markdown>
          </ThemedView>
        ) : null}

        {/* Hint */}
        {step.hint && (
          <ThemedView style={styles.infoSection}>
            <ThemedView style={styles.infoRow}>
              <ThemedView style={[styles.iconContainer, styles.hintIcon]}>
                <Lightbulb size={styles.hintIcon.iconSize} color={styles.hintIcon.iconColor} />
              </ThemedView>
              <ThemedView style={styles.infoContent}>
                <ThemedText type="defaultSemiBold">Hint</ThemedText>
                <ThemedText style={styles.infoText}>{step.hint}</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        )}

        {/* Golden Key */}
        {step.goldenKey && (
          <ThemedView style={styles.infoSection}>
            <ThemedView style={styles.infoRow}>
              <ThemedView style={[styles.iconContainer, styles.goldenKeyIcon]}>
                <Key size={styles.goldenKeyIcon.iconSize} color={styles.goldenKeyIcon.iconColor} />
              </ThemedView>
              <ThemedView style={styles.infoContent}>
                <ThemedText type="defaultSemiBold" style={styles.goldenKeyTitle}>
                  Golden Key #{step.goldenKey.num} — {step.goldenKey.key}
                </ThemedText>
                <ThemedText style={[styles.infoText, styles.goldenKeyText]}>
                  {step.goldenKey.expanded}
                </ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  bibleRefIcon: {
    size: 18,
    color: theme.colors.brand.primary.emphasis,
  },
  maximizeIcon: {
    size: theme.spacing.md,
    color: theme.colors.brand.primary.emphasis,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  backButtonContainer: {
    position: 'absolute',
    top: rt.insets.top,
    left: theme.spacing.md,
    zIndex: 10,
    backgroundColor: 'transparent',
  },
  backButton: {
    width: theme.spacing['2xl'],
    height: theme.spacing['2xl'],
    borderRadius: theme.radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.background.primary,
    ...Platform.select({
      ios: {
        overflow: 'visible',
        shadowColor: 'black',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
      android: {
        overflow: 'hidden',
        elevation: 2,
      },
    }),
  },
  backButtonPressed: {
    backgroundColor: theme.colors.background.secondary,
    transform: [{ scale: 0.97 }],
  },
  scrollContent: {
    paddingTop: rt.insets.top + 56,
    paddingBottom: rt.insets.bottom,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    gap: theme.spacing.xs,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  stepNumber: {
    color: theme.colors.text.secondary,
    letterSpacing: 0.5,
  },
  stepTitle: {
    fontSize: theme.typography.h2.fontSize,
    lineHeight: theme.typography.h2.lineHeight,
    fontFamily: theme.fonts.primary.semiBold,
    color: theme.colors.text.primary,
  },
  bibleRefsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  bibleRefButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.radius.xl,
    gap: theme.spacing.sm,
    backgroundColor: theme.colors.brand.primary.muted,
    boxShadow: `0 2 8 ${theme.colors.text.primary}0D`,
  },
  bibleRefButtonPressed: {
    opacity: theme.colors.opacity,
  },
  bibleRefText: {
    color: theme.colors.brand.primary.emphasis,
  },
  infoSection: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  infoRow: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  iconContainer: {
    width: theme.spacing['2xl'],
    height: theme.spacing['2xl'],
    borderRadius: theme.radius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  infoContent: {
    flex: 1,
    justifyContent: 'center',
  },
  keyPointIcon: {
    backgroundColor: theme.colors.brand.primary.subtle,
    iconSize: theme.spacing.lg,
    iconColor: theme.colors.brand.primary.base,
  },
  hintIcon: {
    backgroundColor: theme.colors.brand.primary.subtle,
    iconSize: theme.spacing.lg,
    iconColor: theme.colors.brand.primary.base,
  },
  goldenKeyIcon: {
    backgroundColor: theme.colors.semantic.action.bg,
    iconSize: theme.spacing.lg,
    iconColor: theme.colors.semantic.action.active,
  },
  infoText: {
    color: theme.colors.text.subdued,
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
  },
  mainContent: {
    gap: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  bodyText: {
    fontFamily: theme.typography.body1.fontFamily,
    fontSize: theme.typography.body1.fontSize,
    lineHeight: theme.typography.body1.lineHeight,
    color: theme.colors.text.primary,
  },
  md_blockquote: {
    backgroundColor: theme.colors.background.primary,
    borderColor: theme.colors.brand.primary.muted,
  },
  md_code_inline: {
    fontFamily: theme.fonts.primary.regular,
    backgroundColor: theme.colors.brand.primary.subtle,
    color: theme.colors.brand.primary.emphasis,
  },
  md_strong: {
    fontFamily: theme.fonts.primary.semiBold,
    fontSize: theme.typography.body1.fontSize,
    lineHeight: theme.typography.body1.lineHeight,
  },
  md_em: {
    fontFamily: theme.fonts.primary.italic,
    fontSize: theme.typography.body1.fontSize,
    lineHeight: theme.typography.body1.lineHeight,
  },
  emphasis: {
    fontFamily: theme.fonts.primary.semiBold,
    color: theme.colors.text.primary,
  },
  italicText: {
    fontStyle: 'italic',
    color: theme.colors.text.tertiary,
  },
  goldenKeyTitle: {
    color: theme.colors.semantic.action.text,
  },
  goldenKeyText: {
    color: theme.colors.semantic.action.text,
  },
}));
