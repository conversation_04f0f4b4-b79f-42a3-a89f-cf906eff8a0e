import { createStyleSheet } from 'react-native-unistyles';

const LessonsStyles = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  categoryList: {
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
    marginRight: theme.spacing.md,
    width: 160,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryTitle: {
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  lessonList: {
    marginBottom: 16,
  },
  lessonCard: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  lessonTitle: {
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  lessonDescription: {
    marginBottom: theme.spacing.sm,
  },
  lessonMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.sm,
  },
  lessonMetaText: {
    fontSize: theme.typography.body2.fontSize,
    color: theme.colors.text.secondary,
  },
  progressContainer: {
    height: 6,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.sm,
    marginTop: theme.spacing.sm,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.brand.primary.base,
  },
  difficultyBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
    marginLeft: theme.spacing.xs,
  },
  beginnerBadge: {
    backgroundColor: 'green',
  },
  intermediateBadge: {
    backgroundColor: 'orange',
  },
  advancedBadge: {
    backgroundColor: 'red',
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: theme.spacing.xs,
  },
  tagBadge: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.sm,
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    marginRight: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  searchInput: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.sm,
    padding: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
}));

export default LessonsStyles;
