import { api } from '@/api';
import { StorageKeys } from '@/constants/StorageKeys';

export interface Lesson {
  id: string;
  title: string;
  description: string;
  content: string;
  imageUrl?: string;
  duration: number; // in minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  author?: string;
  tags?: string[];
  prerequisites?: string[];
  lastUpdated: string;
  isCompleted?: boolean;
  progress?: number; // 0-100
}

export interface LessonCategory {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  lessonIds: string[];
}

// Fetch all lessons
export async function getLessons(): Promise<Lesson[]> {
  try {
    const response = await api.get('/lessons');
    return response.data;
  } catch (error) {
    console.error('Error fetching lessons:', error);
    return [];
  }
}

// Fetch a single lesson by ID
export async function getLesson(id: string): Promise<Lesson | null> {
  try {
    const response = await api.get(`/lessons/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching lesson ${id}:`, error);
    return null;
  }
}

// Fetch all lesson categories
export async function getLessonCategories(): Promise<LessonCategory[]> {
  try {
    const response = await api.get('/lessons/categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching lesson categories:', error);
    return [];
  }
}

// Update lesson progress
export async function updateLessonProgress(id: string, progress: number): Promise<boolean> {
  try {
    await api.put(`/lessons/${id}/progress`, { progress });
    return true;
  } catch (error) {
    console.error(`Error updating lesson progress for ${id}:`, error);
    return false;
  }
}

// Mark a lesson as complete
export async function markLessonComplete(id: string): Promise<boolean> {
  try {
    await api.put(`/lessons/${id}/complete`);
    return true;
  } catch (error) {
    console.error(`Error marking lesson ${id} as complete:`, error);
    return false;
  }
}
