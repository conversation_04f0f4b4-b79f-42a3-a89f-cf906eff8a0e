import { useCallback, useEffect, useState } from 'react';

import {
  getLesson,
  getLessonCategories,
  getLessons,
  markLessonComplete,
  updateLessonProgress,
} from '../api';
import { Lesson, LessonCategory, LessonFilter } from '../types';

export function useLessonData(filter?: LessonFilter) {
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [categories, setCategories] = useState<LessonCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLessons = useCallback(async () => {
    setIsLoading(true);
    try {
      const [lessonsData, categoriesData] = await Promise.all([
        getLessons(),
        getLessonCategories(),
      ]);

      setLessons(lessonsData);
      setCategories(categoriesData);
      setError(null);
    } catch (err) {
      setError('Failed to fetch lessons data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLessons();
  }, [fetchLessons]);

  // Apply filters if provided
  const filteredLessons = useCallback(() => {
    if (!filter) return lessons;

    return lessons.filter((lesson) => {
      // Filter by difficulty
      if (filter.difficulty && lesson.difficulty !== filter.difficulty) return false;

      // Filter by completion status
      if (filter.completed !== undefined && lesson.isCompleted !== filter.completed) return false;

      // Filter by duration
      if (filter.duration?.min !== undefined && lesson.duration < filter.duration.min) return false;
      if (filter.duration?.max !== undefined && lesson.duration > filter.duration.max) return false;

      // Filter by tags
      if (filter.tags && filter.tags.length > 0) {
        if (!lesson.tags) return false;
        const hasMatchingTag = filter.tags.some((tag) => lesson.tags?.includes(tag));
        if (!hasMatchingTag) return false;
      }

      // Filter by search term
      if (filter.searchTerm) {
        const term = filter.searchTerm.toLowerCase();
        const matchesTitle = lesson.title.toLowerCase().includes(term);
        const matchesDescription = lesson.description.toLowerCase().includes(term);
        if (!matchesTitle && !matchesDescription) return false;
      }

      return true;
    });
  }, [lessons, filter]);

  // Function to get lessons by category
  const getLessonsByCategory = useCallback(
    (categoryId: string): Lesson[] => {
      const category = categories.find((cat) => cat.id === categoryId);
      if (!category) return [];

      return lessons.filter((lesson) => category.lessonIds.includes(lesson.id));
    },
    [categories, lessons],
  );

  // Function to update lesson progress
  const updateProgress = useCallback(
    async (lessonId: string, progress: number): Promise<boolean> => {
      const success = await updateLessonProgress(lessonId, progress);

      if (success) {
        // Update local state
        setLessons((prevLessons) =>
          prevLessons.map((lesson) => (lesson.id === lessonId ? { ...lesson, progress } : lesson)),
        );
      }

      return success;
    },
    [],
  );

  // Function to mark a lesson as complete
  const completeLesson = useCallback(async (lessonId: string): Promise<boolean> => {
    const success = await markLessonComplete(lessonId);

    if (success) {
      // Update local state
      setLessons((prevLessons) =>
        prevLessons.map((lesson) =>
          lesson.id === lessonId ? { ...lesson, isCompleted: true, progress: 100 } : lesson,
        ),
      );
    }

    return success;
  }, []);

  return {
    lessons: filter ? filteredLessons() : lessons,
    categories,
    isLoading,
    error,
    refresh: fetchLessons,
    getLessonsByCategory,
    updateProgress,
    completeLesson,
  };
}
