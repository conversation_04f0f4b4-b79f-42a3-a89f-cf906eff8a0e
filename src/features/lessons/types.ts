// Type definitions for the Lessons feature

export interface Lesson {
  id: string;
  title: string;
  description: string;
  content: string;
  imageUrl?: string;
  duration: number; // in minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  author?: string;
  tags?: string[];
  prerequisites?: string[];
  lastUpdated: string;
  isCompleted?: boolean;
  progress?: number; // 0-100
}

export interface LessonCategory {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  lessonIds: string[];
}

export interface LessonState {
  lessons: Lesson[];
  categories: LessonCategory[];
  selectedLesson: Lesson | null;
  selectedCategory: LessonCategory | null;
  isLoading: boolean;
  error: string | null;
}

export interface LessonFilter {
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags?: string[];
  duration?: {
    min?: number;
    max?: number;
  };
  completed?: boolean;
  searchTerm?: string;
}

export interface LessonProgress {
  lessonId: string;
  userId: string;
  progress: number; // 0-100
  completed: boolean;
  lastAccessedDate: string;
  timeSpent: number; // in seconds
}

export interface LessonSettings {
  showCompletedLessons: boolean;
  defaultDifficulty: 'beginner' | 'intermediate' | 'advanced' | null;
  notificationsEnabled: boolean;
  reminderFrequency: 'daily' | 'weekly' | 'monthly' | 'never';
}
