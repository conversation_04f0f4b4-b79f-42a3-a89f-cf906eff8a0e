import { Pressable } from 'react-native';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { type ProspectListItem } from '@/api/schemas/prospects';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import {
  PROSPECT_STATUS_MAP,
  type ProspectStatus,
} from '@/features/prospects/constants/prospectKeys';

interface ProspectCardProps {
  prospect: ProspectListItem;
  onPress?: () => void;
}

export const ProspectCard = ({ prospect, onPress }: ProspectCardProps) => {
  const { styles, theme } = useStyles(prospectCardStyles);

  const statusInfo = PROSPECT_STATUS_MAP[prospect.status as ProspectStatus];
  const statusColor = statusInfo.getColor(theme);

  return (
    <Pressable onPress={onPress}>
      {({ pressed }) => (
        <ThemedView style={[styles.card, pressed && styles.cardPressed]}>
          <ThemedView style={styles.header}>
            <ThemedText style={styles.name}>{prospect.displayName}</ThemedText>
            <ThemedView style={styles.statusContainer}>
              <ThemedText style={styles.statusIcon}>{prospect.statusIcon}</ThemedText>
              <ThemedView style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                <ThemedText style={styles.statusText}>{statusInfo.label}</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.footer}>
            <ThemedText style={styles.lastContact}>
              Last contact: {new Date(prospect.lastContactAt).toLocaleDateString()}
            </ThemedText>
            {prospect.daysSinceUpdate > 0 && (
              <ThemedText
                style={[styles.daysSince, prospect.daysSinceUpdate > 7 && styles.daysSinceOverdue]}
              >
                {prospect.daysSinceUpdate} days since update
              </ThemedText>
            )}
          </ThemedView>
        </ThemedView>
      )}
    </Pressable>
  );
};

const prospectCardStyles = createStyleSheet((theme) => ({
  card: {
    backgroundColor: theme.colors.surface.primary,
    borderRadius: theme.radius.md,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderWidth: 1,
    borderColor: theme.colors.border,
    // Shadow tokens
    shadowColor: theme.colors.shadow,
    ...theme.shadows,
    elevation: 2,
  },
  cardPressed: {
    backgroundColor: theme.colors.surface.secondary,
    transform: [{ scale: 0.98 }],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  name: {
    fontFamily: theme.typography.h4.fontFamily,
    fontSize: theme.typography.h4.fontSize,
    lineHeight: theme.typography.h4.lineHeight,
    fontWeight: theme.typography.h4.fontWeight,
    color: theme.colors.text.primary,
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  statusIcon: {
    fontSize: theme.typography.size.lg,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radius.sm,
  },
  statusText: {
    fontFamily: theme.typography.subtitle2.fontFamily,
    fontSize: theme.typography.subtitle2.fontSize,
    lineHeight: theme.typography.subtitle2.lineHeight,
    fontWeight: theme.typography.subtitle2.fontWeight,
    color: theme.colors.text.inverse,
  },
  footer: {
    gap: theme.spacing.xs,
  },
  lastContact: {
    fontFamily: theme.typography.body2.fontFamily,
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.colors.text.secondary,
  },
  daysSince: {
    fontFamily: theme.typography.subtitle2.fontFamily,
    fontSize: theme.typography.subtitle2.fontSize,
    lineHeight: theme.typography.subtitle2.lineHeight,
    color: theme.colors.text.tertiary,
  },
  daysSinceOverdue: {
    color: theme.colors.semantic.warning.text,
    fontWeight: '500',
  },
}));
