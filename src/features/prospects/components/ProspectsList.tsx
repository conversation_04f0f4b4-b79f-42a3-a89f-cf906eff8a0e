import { FlatList, RefreshControl } from 'react-native';

import { type ApiErrorResponse } from '@/api/client/types';
import { type ProspectListItem } from '@/api/schemas/prospects';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

import { ProspectCard } from './ProspectCard';

interface ProspectsListProps {
  prospects?: ProspectListItem[];
  isLoading?: boolean;
  error?: ApiErrorResponse | null;
  onProspectPress?: (prospect: ProspectListItem) => void;
  refreshing?: boolean;
  onRefresh?: () => void;
}

export const ProspectsList = ({
  prospects,
  isLoading,
  error,
  onProspectPress,
  refreshing,
  onRefresh,
}: ProspectsListProps) => {
  if (isLoading && !prospects) {
    return (
      <ThemedView style={{ padding: 20, alignItems: 'center' }}>
        <ThemedText>Loading prospects...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={{ padding: 20, alignItems: 'center' }}>
        <ThemedText style={{ color: 'red' }}>Error loading prospects</ThemedText>
        <ThemedText style={{ marginTop: 10, fontSize: 12, color: 'gray' }}>
          {error.error?.message || 'Unknown error'}
        </ThemedText>
      </ThemedView>
    );
  }

  if (!prospects || prospects.length === 0) {
    return (
      <ThemedView style={{ padding: 20, alignItems: 'center' }}>
        <ThemedText>No prospects found</ThemedText>
        <ThemedText style={{ marginTop: 10, fontSize: 12, color: 'gray' }}>
          Add your first prospect to get started
        </ThemedText>
      </ThemedView>
    );
  }

  const renderProspectCard = ({ item }: { item: ProspectListItem }) => (
    <ProspectCard prospect={item} onPress={() => onProspectPress?.(item)} />
  );

  return (
    <FlatList
      data={prospects}
      renderItem={renderProspectCard}
      keyExtractor={(item) => item.id}
      refreshControl={
        onRefresh ? (
          <RefreshControl refreshing={refreshing || false} onRefresh={onRefresh} />
        ) : undefined
      }
      contentContainerStyle={{ padding: 16 }}
      showsVerticalScrollIndicator={false}
    />
  );
};
