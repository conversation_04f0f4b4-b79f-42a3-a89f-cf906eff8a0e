// /src/features/prospects/constants/prospectKeys.ts

// Static mappings for Prospect statuses to avoid API lookups
// Updated to match Go backend status progression
// Using theme-aware color function that will be applied at runtime
export const PROSPECT_STATUS_MAP = {
  PROSPECT: {
    label: 'Prospect',
    getColor: (theme: any) => theme.colors.semantic.primary.bg, // Brand primary
    description: 'Person being witnessed to',
    icon: '👤',
  },
  SAVED: {
    label: 'Saved',
    getColor: (theme: any) => theme.colors.semantic.success.bg, // Mint/success
    description: 'Accepted Jesus Christ',
    icon: '✝️',
  },
  BAPTIZED: {
    label: 'Baptized',
    getColor: (theme: any) => theme.colors.semantic.highlight.bg, // Teal/highlight
    description: 'Followed in baptism obedience',
    icon: '💧',
  },
  FOUNDATION: {
    label: 'Foundation',
    getColor: (theme: any) => theme.colors.brand.primary.accent, // Brand accent
    description: 'Learning basics + church integration',
    icon: '📚',
  },
  MINISTRY: {
    label: 'Ministry',
    getColor: (theme: any) => theme.colors.semantic.action.bg, // Orange/action
    description: 'Active in church ministries',
    icon: '⚡',
  },
} as const;

// Priority levels for follow-up
export const PROSPECT_PRIORITY_MAP = {
  HIGH: {
    label: 'High',
    getColor: (theme: any) => theme.colors.semantic.error.bg, // Ruby/error
    description: 'Requires immediate attention',
  },
  MEDIUM: {
    label: 'Medium',
    getColor: (theme: any) => theme.colors.semantic.warning.bg, // Orange/warning
    description: 'Normal follow-up schedule',
  },
  LOW: {
    label: 'Low',
    getColor: (theme: any) => theme.colors.text.secondary, // Neutral/secondary
    description: 'Minimal follow-up needed',
  },
} as const;

// Visit types for categorization
export const VISIT_TYPE_MAP = {
  INITIAL: {
    label: 'Initial Visit',
    icon: 'person-add-outline',
    description: 'First contact with prospect',
  },
  FOLLOW_UP: {
    label: 'Follow-up',
    icon: 'chatbubbles-outline',
    description: 'Regular follow-up visit',
  },
  BIBLE_STUDY: {
    label: 'Bible Study',
    icon: 'book-outline',
    description: 'Bible study session',
  },
  PRAYER: {
    label: 'Prayer',
    icon: 'heart-outline',
    description: 'Prayer and spiritual support',
  },
  BAPTISM: {
    label: 'Baptism',
    icon: 'water-outline',
    description: 'Baptism ceremony',
  },
  OTHER: {
    label: 'Other',
    icon: 'ellipsis-horizontal-outline',
    description: 'Other type of interaction',
  },
} as const;

// Quick action templates for common scenarios
export const QUICK_NOTES_TEMPLATES = {
  FIRST_VISIT: 'First visit completed. Prospect showed interest in learning more.',
  BIBLE_STUDY: 'Conducted Bible study session. Discussed salvation and grace.',
  PRAYER_REQUEST: 'Prayed together. Prospect requested prayer for specific needs.',
  FOLLOW_UP_NEEDED: 'Follow-up needed within the next week.',
  READY_FOR_BAPTISM: 'Prospect expressed interest in baptism.',
  DISCIPLESHIP: 'Beginning discipleship program.',
} as const;

// Default visit durations (in minutes) for scheduling
export const VISIT_DURATION_MAP = {
  INITIAL: 45,
  FOLLOW_UP: 30,
  BIBLE_STUDY: 60,
  PRAYER: 20,
  BAPTISM: 120,
  OTHER: 30,
} as const;

// Export type-safe keys
export type ProspectStatus = keyof typeof PROSPECT_STATUS_MAP;
export type ProspectPriority = keyof typeof PROSPECT_PRIORITY_MAP;
export type VisitType = keyof typeof VISIT_TYPE_MAP;
export type QuickNotesTemplate = keyof typeof QUICK_NOTES_TEMPLATES;
