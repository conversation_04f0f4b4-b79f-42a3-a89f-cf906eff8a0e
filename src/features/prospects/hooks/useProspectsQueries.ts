/**
 * Prospects query hooks - Using validated queries with Go backend
 * Following Gospel feature pattern with standard MMKV persistence
 */
import { useMutation, useQueryClient } from '@tanstack/react-query';

import * as prospects from '@/api/endpoints/prospects';
import { CACHE_KEYS } from '@/api/hooks/useApiQuery';
import { useValidatedQuery } from '@/api/hooks/useValidatedQuery';
import {
  type CreateProspectRequest,
  DashboardResponseSchema,
  ProspectDetailResponseSchema,
  type ProspectListItem,
  ProspectListResponseSchema,
  ProspectStatsResponseSchema,
  SearchProspectsResponseSchema,
  type UpdateProspectRequest,
  type UpdateStatusRequest,
} from '@/api/schemas/prospects';

// Standard cache configuration for Prospects (not infinite like Gospel)
const PROSPECTS_CACHE_CONFIG = {
  staleTime: 15 * 60 * 1000, // 15 minutes (matches backend Redis TTL)
  gcTime: 24 * 60 * 60 * 1000, // 24 hours MMKV persistence
};

/**
 * Get user's prospect list with validation
 */
export const useProspects = () => {
  return useValidatedQuery(
    [CACHE_KEYS.prospects.list],
    () => prospects.getProspects(),
    ProspectListResponseSchema,
    {
      select: (response) => response.prospects,
      ...PROSPECTS_CACHE_CONFIG,
    },
  );
};

/**
 * Search prospects with debounced query
 */
export const useSearchProspects = (query: string, enabled: boolean = true) => {
  return useValidatedQuery(
    [CACHE_KEYS.prospects.search, query],
    () => prospects.searchProspects(query),
    SearchProspectsResponseSchema,
    {
      select: (response) => response.prospects,
      enabled: enabled && query.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes for search results
      gcTime: 10 * 60 * 1000, // 10 minutes for search cache
    },
  );
};

/**
 * Get prospect detail with validation
 */
export const useProspectDetail = (id: string) => {
  return useValidatedQuery(
    [CACHE_KEYS.prospects.detail, id],
    () => prospects.getProspectDetail(id),
    ProspectDetailResponseSchema,
    {
      ...PROSPECTS_CACHE_CONFIG,
      enabled: !!id,
    },
  );
};

/**
 * Get dashboard data with weekly summary and reminders
 */
export const useDashboard = () => {
  return useValidatedQuery(
    [CACHE_KEYS.prospects.dashboard],
    () => prospects.getDashboard(),
    DashboardResponseSchema,
    {
      staleTime: 10 * 60 * 1000, // 10 minutes for dashboard (more frequent updates)
      gcTime: 30 * 60 * 1000, // 30 minutes cache
    },
  );
};

/**
 * Get prospect statistics
 */
export const useProspectStats = () => {
  return useValidatedQuery(
    [CACHE_KEYS.prospects.stats],
    () => prospects.getProspectStats(),
    ProspectStatsResponseSchema,
    {
      staleTime: 30 * 60 * 1000, // 30 minutes for stats
      gcTime: 2 * 60 * 60 * 1000, // 2 hours cache
    },
  );
};

/**
 * Create prospect mutation with optimistic updates
 */
export const useCreateProspect = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProspectRequest) => prospects.createProspect(data),
    onMutate: async (newProspect) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: [CACHE_KEYS.prospects.list] });

      // Snapshot previous value
      const previousProspects = queryClient.getQueryData<ProspectListItem[]>([
        CACHE_KEYS.prospects.list,
      ]);

      // Optimistically update prospect list
      if (previousProspects) {
        const optimisticProspect: ProspectListItem = {
          id: `temp-${Date.now()}`,
          displayName: newProspect.nickName || `${newProspect.firstName} ${newProspect.lastName}`,
          status: newProspect.status || 'PROSPECT',
          daysSinceUpdate: 0,
          statusIcon: '👤',
          lastContactAt: new Date().toISOString(),
        };

        queryClient.setQueryData(
          [CACHE_KEYS.prospects.list],
          [optimisticProspect, ...previousProspects],
        );
      }

      return { previousProspects };
    },
    onError: (err, _newProspect, context) => {
      // Revert optimistic update on error
      if (context?.previousProspects) {
        queryClient.setQueryData([CACHE_KEYS.prospects.list], context.previousProspects);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch prospect list and dashboard
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.list] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.dashboard] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.stats] });
    },
  });
};

/**
 * Update prospect mutation with optimistic updates
 */
export const useUpdateProspect = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProspectRequest }) =>
      prospects.updateProspect(id, data),
    onSuccess: (updatedProspect, { id }) => {
      // Update detail cache
      queryClient.setQueryData([CACHE_KEYS.prospects.detail, id], updatedProspect);

      // Invalidate list and dashboard
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.list] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.dashboard] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.stats] });
    },
  });
};

/**
 * Update prospect status mutation with validation
 */
export const useUpdateProspectStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: UpdateStatusRequest }) =>
      prospects.updateProspectStatus(id, status),
    onSuccess: (updatedProspect, { id }) => {
      // Update detail cache
      queryClient.setQueryData([CACHE_KEYS.prospects.detail, id], updatedProspect);

      // Invalidate list and dashboard (status changes affect dashboard metrics)
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.list] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.dashboard] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.stats] });
    },
  });
};

/**
 * Delete prospect mutation
 */
export const useDeleteProspect = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => prospects.deleteProspect(id),
    onMutate: async (deletedId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: [CACHE_KEYS.prospects.list] });

      // Snapshot previous value
      const previousProspects = queryClient.getQueryData<ProspectListItem[]>([
        CACHE_KEYS.prospects.list,
      ]);

      // Optimistically remove prospect from list
      if (previousProspects) {
        queryClient.setQueryData(
          [CACHE_KEYS.prospects.list],
          previousProspects.filter((prospect) => prospect.id !== deletedId),
        );
      }

      return { previousProspects };
    },
    onError: (_err, _deletedId, context) => {
      // Revert optimistic update on error
      if (context?.previousProspects) {
        queryClient.setQueryData([CACHE_KEYS.prospects.list], context.previousProspects);
      }
    },
    onSuccess: (_, deletedId) => {
      // Remove from detail cache
      queryClient.removeQueries({ queryKey: [CACHE_KEYS.prospects.detail, deletedId] });

      // Invalidate dashboard and stats
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.dashboard] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.prospects.stats] });
    },
  });
};
