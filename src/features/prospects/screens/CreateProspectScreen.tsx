import { useRef } from 'react';

import {
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  View,
} from 'react-native';

import { useRouter } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { Controller, useForm } from 'react-hook-form';

import { type CreateProspectRequest } from '@/api/schemas/prospects';
import { Button } from '@/components/Button';
import { ThemedView } from '@/components/ThemedView';
import { FormInput } from '@/components/form/FormInput';
import { useCreateProspect } from '@/features/prospects/hooks';
import { useTabBarHeight } from '@/hooks/ui/useTabbarHeight';

export const CreateProspectScreen = () => {
  const router = useRouter();
  const { styles, theme } = useStyles(stylesheet);
  const tabBarHeight = useTabBarHeight();

  const lastNameRef = useRef<TextInput>(null);
  const nickNameRef = useRef<TextInput>(null);
  const contactInfoRef = useRef<TextInput>(null);
  const addressRef = useRef<TextInput>(null);
  const notesRef = useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateProspectRequest>({
    defaultValues: {
      firstName: '',
      lastName: '',
      nickName: '',
      contactInfo: '',
      address: '',
      notes: '',
    },
  });

  const createProspect = useCreateProspect();

  const onSubmit = async (data: CreateProspectRequest) => {
    try {
      const newProspect = await createProspect.mutateAsync(data);
      Alert.alert('Success', 'Prospect created successfully', [
        {
          text: 'View Prospect',
          onPress: () => router.push(`/gospel/prospects/${newProspect.id}`),
        },
        {
          text: 'Add Another',
          onPress: () => reset(),
          style: 'cancel',
        },
      ]);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to create prospect. Please try again.';

      Alert.alert('Error', errorMessage, [{ text: 'OK' }]);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ThemedView style={styles.container}>
        <ScrollView
          contentContainerStyle={[
            styles.scrollContent,
            { paddingBottom: tabBarHeight + theme.spacing['2xl'] },
          ]}
          keyboardShouldPersistTaps="handled"
          contentInsetAdjustmentBehavior="automatic"
          scrollIndicatorInsets={{ bottom: tabBarHeight }}
        >
          <View style={styles.form}>
            <Controller
              control={control}
              name="firstName"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  label="First Name"
                  value={value}
                  onChangeText={onChange}
                  error={errors.firstName?.message}
                  autoCapitalize="words"
                  returnKeyType="next"
                  onSubmitEditing={() => lastNameRef.current?.focus()}
                  required
                />
              )}
            />

            <Controller
              control={control}
              name="lastName"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  ref={lastNameRef}
                  label="Last Name"
                  value={value}
                  onChangeText={onChange}
                  error={errors.lastName?.message}
                  autoCapitalize="words"
                  returnKeyType="next"
                  onSubmitEditing={() => nickNameRef.current?.focus()}
                  required
                />
              )}
            />

            <Controller
              control={control}
              name="nickName"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  ref={nickNameRef}
                  label="Nickname (Optional)"
                  placeholder="What they prefer to be called"
                  value={value}
                  onChangeText={onChange}
                  error={errors.nickName?.message}
                  autoCapitalize="words"
                  returnKeyType="next"
                  onSubmitEditing={() => contactInfoRef.current?.focus()}
                />
              )}
            />

            <Controller
              control={control}
              name="contactInfo"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  ref={contactInfoRef}
                  label="Contact Info (Optional)"
                  placeholder="Phone number or email address"
                  value={value}
                  onChangeText={onChange}
                  error={errors.contactInfo?.message}
                  keyboardType="default"
                  autoCapitalize="none"
                  returnKeyType="next"
                  onSubmitEditing={() => addressRef.current?.focus()}
                />
              )}
            />

            <Controller
              control={control}
              name="address"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  ref={addressRef}
                  label="Address"
                  value={value}
                  onChangeText={onChange}
                  error={errors.address?.message}
                  multiline
                  numberOfLines={2}
                  onSubmitEditing={() => notesRef.current?.focus()}
                />
              )}
            />

            <Controller
              control={control}
              name="notes"
              render={({ field: { onChange, value } }) => (
                <FormInput
                  ref={notesRef}
                  label="Notes"
                  value={value}
                  onChangeText={onChange}
                  error={errors.notes?.message}
                  multiline
                  numberOfLines={3}
                  placeholder="Any additional information about this prospect..."
                  onSubmitEditing={() => Keyboard.dismiss()}
                />
              )}
            />

            <View style={styles.buttonContainer}>
              <Button
                variant="primary"
                size="lg"
                fullWidth
                loading={createProspect.isPending}
                disabled={createProspect.isPending}
                onPress={handleSubmit(onSubmit)}
                accessibilityLabel="Create prospect"
                accessibilityHint="Creates a new prospect with the entered information"
                accessibilityRole="button"
              >
                Create Prospect
              </Button>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollContent: {
    padding: theme.spacing.lg,
  },
  form: {
    gap: theme.spacing.lg,
  },
  buttonContainer: {
    marginTop: theme.spacing.xl,
  },
}));
