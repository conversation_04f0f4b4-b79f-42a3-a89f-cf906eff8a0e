import { Alert, Pressable, ScrollView } from 'react-native';

import { Stack, useLocalSearchParams, useRouter } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { Calendar, Edit, Mail, MapPin, Phone } from 'lucide-react-native';

import { type ApiErrorResponse } from '@/api/client/types';
import { Button } from '@/components/Button';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import {
  PROSPECT_STATUS_MAP,
  type ProspectStatus,
} from '@/features/prospects/constants/prospectKeys';
import { useProspectDetail } from '@/features/prospects/hooks';
import { useTabBarHeight } from '@/hooks/ui/useTabbarHeight';

export const ProspectDetailScreen = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { styles, theme } = useStyles(stylesheet);
  const tabBarHeight = useTabBarHeight();

  const { data: prospect, isLoading, error } = useProspectDetail(id!);

  if (isLoading && !prospect) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.centered}>
          <ThemedText>Loading prospect details...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.centered}>
          <ThemedText style={styles.errorText}>Error loading prospect</ThemedText>
          <ThemedText style={styles.errorSubtext}>
            {(error as ApiErrorResponse)?.error?.message || 'Unknown error'}
          </ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  if (!prospect) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.centered}>
          <ThemedText>Prospect not found</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  const statusInfo = PROSPECT_STATUS_MAP[prospect.status as ProspectStatus];
  const statusColor = statusInfo.getColor(theme);
  const displayName = prospect.name.nickName || prospect.name.fullName;
  const daysSinceUpdate = Math.max(
    0,
    Math.floor((Date.now() - new Date(prospect.updatedAt).getTime()) / (1000 * 60 * 60 * 24)),
  );

  const statusTextColorMap: Record<ProspectStatus, string> = {
    PROSPECT: theme.colors.semantic.primary.active,
    SAVED: theme.colors.semantic.success.active,
    BAPTIZED: theme.colors.semantic.highlight.active,
    FOUNDATION: theme.colors.brand.primary.emphasis,
    MINISTRY: theme.colors.semantic.action.active,
  } as const;
  const statusTextColor = statusTextColorMap[prospect.status as ProspectStatus];

  const handleEdit = () => {
    router.push(`/gospel/prospects/edit?id=${prospect.id}`);
  };

  const handleCall = () => {
    if (prospect.contactInfo) {
      // TODO: Implement phone call functionality
      Alert.alert('Call', `Would you like to call ${prospect.contactInfo}?`);
    }
  };

  const handleEmail = () => {
    if (prospect.contactInfo && prospect.contactInfo.includes('@')) {
      // TODO: Implement email functionality
      Alert.alert('Email', `Would you like to email ${prospect.contactInfo}?`);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <Stack.Screen
        options={{
          headerTitle: displayName,
          headerRight: () => (
            <Pressable
              onPress={handleEdit}
              style={({ pressed }) => [
                {
                  padding: theme.spacing.xs,
                  borderRadius: theme.radius.sm,
                  backgroundColor: pressed ? theme.colors.surface.secondary : 'transparent',
                },
              ]}
              accessibilityLabel="Edit prospect"
              accessibilityRole="button"
            >
              <Edit size={20} color={theme.colors.brand.primary.base} />
            </Pressable>
          ),
        }}
      />
      <ScrollView
        contentContainerStyle={[
          styles.scrollContent,
          {
            paddingBottom: tabBarHeight + theme.spacing['2xl'],
          },
        ]}
        contentInsetAdjustmentBehavior="automatic"
        scrollIndicatorInsets={{ bottom: tabBarHeight }}
      >
        {/* Contact Information */}
        <ThemedView
          style={[
            styles.section,
            styles.card,
            styles.cardAccentLeft,
            { borderLeftColor: statusColor },
          ]}
        >
          <ThemedView style={styles.sectionHeaderRow}>
            <ThemedText style={styles.sectionTitle}>Contact Information</ThemedText>
            <ThemedView style={[styles.sectionChip, { backgroundColor: statusColor }]}>
              <ThemedText style={[styles.sectionChipText, { color: statusTextColor }]}>
                {statusInfo.icon} {statusInfo.label}
              </ThemedText>
            </ThemedView>
          </ThemedView>

          {prospect.contactInfo && (
            <Pressable
              onPress={prospect.contactInfo.includes('@') ? handleEmail : handleCall}
              style={styles.contactRow}
            >
              {prospect.contactInfo.includes('@') ? (
                <Mail size={18} color={theme.colors.brand.primary.base} />
              ) : (
                <Phone size={18} color={theme.colors.brand.primary.base} />
              )}
              <ThemedText style={styles.contactText}>{prospect.contactInfo}</ThemedText>
            </Pressable>
          )}

          {prospect.address && (
            <ThemedView style={styles.contactRow}>
              <MapPin size={18} color={theme.colors.brand.primary.base} />
              <ThemedText style={styles.contactText}>{prospect.address}</ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        {/* Notes */}
        {prospect.notes && (
          <ThemedView
            style={[
              styles.section,
              styles.card,
              styles.cardAccentLeft,
              { borderLeftColor: statusColor },
            ]}
          >
            <ThemedText style={styles.sectionTitle}>Notes</ThemedText>
            <ThemedText style={styles.notes}>{prospect.notes}</ThemedText>
          </ThemedView>
        )}

        {/* Dates */}
        <ThemedView
          style={[
            styles.section,
            styles.card,
            styles.cardAccentLeft,
            { borderLeftColor: statusColor },
          ]}
        >
          <ThemedText style={styles.sectionTitle}>Information</ThemedText>

          <ThemedView style={styles.dateRow}>
            <Calendar size={18} color={theme.colors.text.secondary} />
            <ThemedView>
              <ThemedText style={styles.dateLabel}>Added</ThemedText>
              <ThemedText style={styles.dateValue}>
                {new Date(prospect.createdAt).toLocaleDateString()}
              </ThemedText>
            </ThemedView>
          </ThemedView>

          {prospect.lastContactAt && (
            <ThemedView style={[styles.dateRow, styles.rowDivider]}>
              <Calendar size={18} color={theme.colors.text.secondary} />
              <ThemedView>
                <ThemedText style={styles.dateLabel}>Last Contact</ThemedText>
                <ThemedText style={styles.dateValue}>
                  {new Date(prospect.lastContactAt).toLocaleDateString()}
                </ThemedText>
              </ThemedView>
            </ThemedView>
          )}

          <ThemedView style={styles.dateRow}>
            <Calendar
              size={18}
              color={
                daysSinceUpdate > 7
                  ? theme.colors.semantic.warning.text
                  : theme.colors.text.secondary
              }
            />
            <ThemedView>
              <ThemedText style={styles.dateLabel}>Updated</ThemedText>
              <ThemedText
                style={[styles.dateValue, daysSinceUpdate > 7 && styles.dateValueWarning]}
              >
                {daysSinceUpdate === 0
                  ? 'Today'
                  : `${daysSinceUpdate} day${daysSinceUpdate > 1 ? 's' : ''} ago`}
              </ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Actions */}
        <ThemedView style={styles.actions}>
          <Button
            variant="primary"
            size="lg"
            fullWidth
            onPress={() => {
              // TODO: Implement visit record functionality
              Alert.alert('Coming Soon', 'Visit recording feature will be available soon');
            }}
          >
            Record Visit
          </Button>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollContent: {
    padding: theme.spacing.md,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  errorText: {
    ...theme.typography.body1,
    color: theme.colors.semantic.error.text,
    marginBottom: theme.spacing.sm,
  },
  errorSubtext: {
    ...theme.typography.subtitle1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  header: {
    marginBottom: theme.spacing.lg,
  },
  name: {
    fontFamily: theme.typography.h2.fontFamily,
    fontSize: theme.typography.h2.fontSize,
    lineHeight: theme.typography.h2.lineHeight,
    fontWeight: theme.typography.h2.fontWeight,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  fullName: {
    fontFamily: theme.typography.body2.fontFamily,
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.sm,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radius.sm,
  },
  statusText: {
    fontFamily: theme.typography.subtitle2.fontFamily,
    fontSize: theme.typography.subtitle2.fontSize,
    lineHeight: theme.typography.subtitle2.lineHeight,
    fontWeight: theme.typography.subtitle2.fontWeight,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: theme.radius.full,
  },
  topStatusRow: {
    marginBottom: theme.spacing.lg,
    alignSelf: 'stretch',
  },

  card: {
    backgroundColor: theme.colors.surface.primary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
  },
  cardAccentLeft: {
    borderLeftWidth: 3,
    paddingLeft: theme.spacing.sm,
  },
  flatSection: {
    backgroundColor: 'transparent',
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
  },
  rowDivider: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: theme.spacing.sm,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontFamily: theme.typography.h4.fontFamily,
    fontSize: theme.typography.h4.fontSize,
    lineHeight: theme.typography.h4.lineHeight,
    fontWeight: theme.typography.h4.fontWeight,
    color: theme.colors.text.primary,
  },
  sectionChip: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.xl,
  },
  sectionChipText: {
    fontFamily: theme.typography.subtitle2.fontFamily,
    fontSize: theme.typography.subtitle2.fontSize,
    lineHeight: theme.typography.subtitle2.lineHeight,
    fontWeight: theme.typography.subtitle2.fontWeight,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  contactText: {
    ...theme.typography.body1,
    color: theme.colors.text.primary,
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  notes: {
    ...theme.typography.body1,
    color: theme.colors.text.secondary,
    lineHeight: theme.typography.lineHeight.relaxed,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  dateLabel: {
    ...theme.typography.subtitle1,
    color: theme.colors.text.secondary,
    marginLeft: theme.spacing.md,
  },
  dateValue: {
    ...theme.typography.body1,
    color: theme.colors.text.primary,
    marginLeft: theme.spacing.md,
    fontWeight: '500',
  },
  dateValueWarning: {
    color: theme.colors.semantic.warning.text,
  },
  actions: {
    marginTop: theme.spacing.lg,
  },
}));
