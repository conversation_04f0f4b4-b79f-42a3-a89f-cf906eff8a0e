import { useCallback } from 'react';

import { useRouter } from 'expo-router';

import { type ApiErrorResponse } from '@/api/client/types';
import { type ProspectListItem } from '@/api/schemas/prospects';
import { ThemedView } from '@/components/ThemedView';
import { useProspects } from '@/features/prospects/hooks';

import { ProspectsList } from '../components';

export const ProspectsListScreen = () => {
  const router = useRouter();
  const { data: prospects, isLoading, error, refetch, isRefetching } = useProspects();

  const handleProspectPress = useCallback(
    (prospect: ProspectListItem) => {
      router.push(`/gospel/prospects/${prospect.id}`);
    },
    [router],
  );

  const handleRefresh = useCallback(async () => {
    refetch();
  }, [refetch]);

  return (
    <ThemedView style={{ flex: 1 }}>
      <ProspectsList
        prospects={prospects}
        isLoading={isLoading}
        error={error}
        onProspectPress={handleProspectPress}
        refreshing={isRefetching}
        onRefresh={handleRefresh}
      />
    </ThemedView>
  );
};
