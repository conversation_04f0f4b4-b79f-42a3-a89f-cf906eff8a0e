import { createStyleSheet } from 'react-native-unistyles';

const SupportStyles = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: theme.spacing.sm,
  },
  cardContainer: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  cardTitle: {
    marginBottom: theme.spacing.xs,
  },
  cardContent: {
    marginBottom: theme.spacing.sm,
  },
  iconContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: theme.radius.full,
    marginRight: theme.spacing.sm,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  labelText: {
    width: 80,
    color: theme.colors.text.secondary,
  },
  valueText: {
    flex: 1,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.md,
  },
  formContainer: {
    marginBottom: theme.spacing.lg,
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
  },
  input: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.sm,
    padding: theme.spacing.sm,
    color: theme.colors.text.primary,
  },
  textArea: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.sm,
    padding: theme.spacing.sm,
    minHeight: 120,
    color: theme.colors.text.primary,
  },
  donationCard: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  donationAmount: {
    fontSize: theme.typography.h3.fontSize,
    fontWeight: 'bold',
    marginBottom: theme.spacing.xs,
  },
  donationDate: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.body2.fontSize,
  },
  recurringBadge: {
    backgroundColor: 'green',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
    alignSelf: 'flex-start',
    marginBottom: theme.spacing.xs,
  },
  recurringText: {
    color: 'white',
    fontSize: theme.typography.caption.fontSize,
  },
  faqItem: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  faqQuestion: {
    fontWeight: 'bold',
    marginBottom: theme.spacing.xs,
  },
  faqAnswer: {
    color: theme.colors.text.secondary,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 2,
  },
  activeTab: {
    borderBottomColor: theme.colors.brand.primary.base,
  },
  inactiveTab: {
    borderBottomColor: 'transparent',
  },
  tabText: {
    color: theme.colors.text.secondary,
  },
  activeTabText: {
    color: theme.colors.brand.primary.base,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.radius.sm,
    alignSelf: 'flex-start',
  },
  openStatus: {
    backgroundColor: 'blue',
  },
  inProgressStatus: {
    backgroundColor: 'orange',
  },
  resolvedStatus: {
    backgroundColor: 'green',
  },
  closedStatus: {
    backgroundColor: 'red',
  },
}));

export default SupportStyles;
