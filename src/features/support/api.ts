import { api } from '@/api';

export interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  userId?: string;
  category?: string;
  assignedTo?: string;
  attachments?: string[];
}

export interface DonationInfo {
  id: string;
  amount: number;
  currency: string;
  date: string;
  paymentMethod: string;
  isRecurring: boolean;
  frequency?: 'monthly' | 'quarterly' | 'annually';
  donorName?: string;
  donorEmail?: string;
  campaign?: string;
  isAnonymous: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
}

// Fetch support tickets
export async function getSupportTickets(userId?: string): Promise<SupportTicket[]> {
  try {
    const url = userId ? `/support/tickets?userId=${userId}` : '/support/tickets';
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    return [];
  }
}

// Create a new support ticket
export async function createSupportTicket(
  ticketData: Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<SupportTicket | null> {
  try {
    const response = await api.post('/support/tickets', ticketData);
    return response.data;
  } catch (error) {
    console.error('Error creating support ticket:', error);
    return null;
  }
}

// Update an existing support ticket
export async function updateSupportTicket(
  id: string,
  ticketData: Partial<SupportTicket>,
): Promise<SupportTicket | null> {
  try {
    const response = await api.put(`/support/tickets/${id}`, ticketData);
    return response.data;
  } catch (error) {
    console.error(`Error updating support ticket ${id}:`, error);
    return null;
  }
}

// Fetch donation history
export async function getDonationHistory(userId?: string): Promise<DonationInfo[]> {
  try {
    const url = userId ? `/support/donations?userId=${userId}` : '/support/donations';
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching donation history:', error);
    return [];
  }
}

// Process a new donation
export async function processDonation(
  donationData: Omit<DonationInfo, 'id' | 'date'>,
): Promise<DonationInfo | null> {
  try {
    const response = await api.post('/support/donations', donationData);
    return response.data;
  } catch (error) {
    console.error('Error processing donation:', error);
    return null;
  }
}

// Fetch FAQ items
export async function getFAQItems(category?: string): Promise<FAQItem[]> {
  try {
    const url = category ? `/support/faq?category=${category}` : '/support/faq';
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching FAQ items:', error);
    return [];
  }
}
