import { useCallback, useEffect, useState } from 'react';

import { FAQItem, getDonationHistory, getFAQItems, getSupportTickets } from '../api';
import { DonationFilter, DonationInfo, FAQFilter, SupportFilter, SupportTicket } from '../types';

export function useSupportData({
  ticketFilter,
  donationFilter,
  faqFilter,
}: {
  ticketFilter?: SupportFilter;
  donationFilter?: DonationFilter;
  faqFilter?: FAQFilter;
} = {}) {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [donations, setDonations] = useState<DonationInfo[]>([]);
  const [faqs, setFaqs] = useState<FAQItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSupportData = useCallback(async () => {
    setIsLoading(true);
    try {
      const [ticketsData, donationsData, faqsData] = await Promise.all([
        getSupportTickets(),
        getDonationHistory(),
        getFAQItems(),
      ]);

      setTickets(ticketsData);
      setDonations(donationsData);
      setFaqs(faqsData);
      setError(null);
    } catch (err) {
      setError('Failed to fetch support data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSupportData();
  }, [fetchSupportData]);

  // Apply filters for tickets
  const filteredTickets = useCallback(() => {
    if (!ticketFilter) return tickets;

    return tickets.filter((ticket) => {
      // Filter by status
      if (ticketFilter.status && ticket.status !== ticketFilter.status) return false;

      // Filter by priority
      if (ticketFilter.priority && ticket.priority !== ticketFilter.priority) return false;

      // Filter by category
      if (ticketFilter.category && ticket.category !== ticketFilter.category) return false;

      // Filter by date range
      if (ticketFilter.startDate) {
        const ticketDate = new Date(ticket.createdAt);
        if (ticketDate < ticketFilter.startDate) return false;
      }

      if (ticketFilter.endDate) {
        const ticketDate = new Date(ticket.createdAt);
        if (ticketDate > ticketFilter.endDate) return false;
      }

      // Filter by search term
      if (ticketFilter.searchTerm) {
        const term = ticketFilter.searchTerm.toLowerCase();
        const matchesTitle = ticket.title.toLowerCase().includes(term);
        const matchesDescription = ticket.description.toLowerCase().includes(term);
        if (!matchesTitle && !matchesDescription) return false;
      }

      return true;
    });
  }, [tickets, ticketFilter]);

  // Apply filters for donations
  const filteredDonations = useCallback(() => {
    if (!donationFilter) return donations;

    return donations.filter((donation) => {
      // Filter by amount range
      if (donationFilter.minAmount !== undefined && donation.amount < donationFilter.minAmount)
        return false;
      if (donationFilter.maxAmount !== undefined && donation.amount > donationFilter.maxAmount)
        return false;

      // Filter by recurring status
      if (
        donationFilter.isRecurring !== undefined &&
        donation.isRecurring !== donationFilter.isRecurring
      )
        return false;

      // Filter by campaign
      if (donationFilter.campaign && donation.campaign !== donationFilter.campaign) return false;

      // Filter by date range
      if (donationFilter.startDate) {
        const donationDate = new Date(donation.date);
        if (donationDate < donationFilter.startDate) return false;
      }

      if (donationFilter.endDate) {
        const donationDate = new Date(donation.date);
        if (donationDate > donationFilter.endDate) return false;
      }

      return true;
    });
  }, [donations, donationFilter]);

  // Apply filters for FAQs
  const filteredFAQs = useCallback(() => {
    if (!faqFilter) return faqs;

    return faqs.filter((faq) => {
      // Filter by category
      if (faqFilter.category && faq.category !== faqFilter.category) return false;

      // Filter by tags
      if (faqFilter.tags && faqFilter.tags.length > 0) {
        if (!faq.tags) return false;
        const hasMatchingTag = faqFilter.tags.some((tag) => faq.tags?.includes(tag));
        if (!hasMatchingTag) return false;
      }

      // Filter by search term
      if (faqFilter.searchTerm) {
        const term = faqFilter.searchTerm.toLowerCase();
        const matchesQuestion = faq.question.toLowerCase().includes(term);
        const matchesAnswer = faq.answer.toLowerCase().includes(term);
        if (!matchesQuestion && !matchesAnswer) return false;
      }

      return true;
    });
  }, [faqs, faqFilter]);

  return {
    tickets: ticketFilter ? filteredTickets() : tickets,
    donations: donationFilter ? filteredDonations() : donations,
    faqs: faqFilter ? filteredFAQs() : faqs,
    isLoading,
    error,
    refresh: fetchSupportData,
  };
}
