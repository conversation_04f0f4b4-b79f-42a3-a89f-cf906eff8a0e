import { createStyleSheet, useStyles } from 'react-native-unistyles';

import ColorList from '@/components/ColorList';
import ImageParallaxScrollView from '@/components/ImageParallaxScrollView';

export function SupportScreen() {
  const { styles } = useStyles(stylesheet);

  return (
    <ImageParallaxScrollView headerImageUri="https://picsum.photos/800/400">
      <ColorList color={styles.colorList.color} />
    </ImageParallaxScrollView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  colorList: {
    color: theme.colors.brand.primary.interaction,
  },
}));
