// Type definitions for the Support feature

export interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  userId?: string;
  category?: string;
  assignedTo?: string;
  attachments?: string[];
}

export interface DonationInfo {
  id: string;
  amount: number;
  currency: string;
  date: string;
  paymentMethod: string;
  isRecurring: boolean;
  frequency?: 'monthly' | 'quarterly' | 'annually';
  donorName?: string;
  donorEmail?: string;
  campaign?: string;
  isAnonymous: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
}

export interface SupportState {
  tickets: SupportTicket[];
  donations: DonationInfo[];
  faqs: FAQItem[];
  selectedTicket: SupportTicket | null;
  isLoading: boolean;
  error: string | null;
}

export interface SupportFilter {
  status?: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  category?: string;
  searchTerm?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface DonationFilter {
  minAmount?: number;
  maxAmount?: number;
  isRecurring?: boolean;
  campaign?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface FAQFilter {
  category?: string;
  tags?: string[];
  searchTerm?: string;
}

export interface ContactInfo {
  email: string;
  phone?: string;
  message: string;
  name?: string;
  subject?: string;
  preferredContactMethod?: 'email' | 'phone';
}
