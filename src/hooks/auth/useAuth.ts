import { useCallback } from 'react';

import { useAtom } from 'jotai';

import { api } from '@/api';
import { kindeAuth } from '@/libs/auth/kindeAuth';
import {
  type UserProfile,
  authErrorAtom,
  isAuthenticatedAtom,
  isLoading<PERSON>tom,
  userProfileAtom,
} from '@/state/atoms/auth';

export function useAuth() {
  const [isLoading, setIsLoading] = useAtom(isLoadingAtom);
  const [isAuthenticated, setIsAuthenticated] = useAtom(isAuthenticatedAtom);
  const [userProfile, setUserProfile] = useAtom(userProfileAtom);
  const [error, setError] = useAtom(authErrorAtom);

  const resetAuthState = useCallback(async () => {
    setIsAuthenticated(false);
    await setUserProfile(null);
    setError(null);
  }, [setIsAuthenticated, setUserProfile, setError]);

  const checkAuth = useCallback(async () => {
    if (isLoading) return; // Prevent multiple simultaneous checks
    try {
      setIsLoading(true);
      setError(null);
      const isAuthed = await kindeAuth.isAuthenticated();
      setIsAuthenticated(isAuthed);

      if (isAuthed) {
        const profile = await kindeAuth.getUserProfile();

        await setUserProfile(profile as UserProfile);
      } else {
        await resetAuthState();
      }
    } catch (err) {
      console.log('Auth check failed:', err);
      await resetAuthState();
      setError('Failed to check authentication status');
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, setIsLoading, setError, setIsAuthenticated, setUserProfile, resetAuthState]);

  const login = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await kindeAuth.login();
      if (result) {
        // Get access token for API calls
        const accessToken = await kindeAuth.getValidAccessToken();

        if (accessToken) {
          // TODO: Implement user profile sync when user API endpoints are available
          // await api.users.updateProfile(result.profile);
          console.log('User profile sync will be implemented when user endpoints are ready');
        }

        setIsAuthenticated(true);
        await setUserProfile(result.profile as UserProfile);
      }
    } catch (err) {
      console.log('Login failed:', err);
      await resetAuthState();
      setError('Login failed. Please try again.');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await kindeAuth.register();
      if (result) {
        setIsAuthenticated(true);
        await setUserProfile(result.profile as UserProfile);
      }
    } catch (err) {
      console.log('Registration failed:', err);
      await resetAuthState();
      setError('Registration failed. Please try again.');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await kindeAuth.logout();
      await resetAuthState();
    } catch (err) {
      console.log('Logout failed:', err);
      setError('Logout failed. Please try again.');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    isAuthenticated,
    userProfile,
    error,
    login,
    logout,
    register,
    checkAuth,
  };
}
