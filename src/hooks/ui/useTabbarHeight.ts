import { useCallback } from 'react';

import { LayoutChangeEvent } from 'react-native';

import { useAtomValue, useSetAtom } from 'jotai';

import { tabBarHeightAtom } from '@/state/persistence/settings';

export function useTabBarHeight() {
  return useAtomValue(tabBarHeightAtom);
}

export function useTabBarMeasure() {
  const setHeight = useSetAtom(tabBarHeightAtom);

  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      setHeight(height);
    },
    [setHeight],
  );

  return { onLayout };
}
