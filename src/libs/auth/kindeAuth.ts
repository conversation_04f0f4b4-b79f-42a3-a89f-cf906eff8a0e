import { Platform } from 'react-native';

import Constants from 'expo-constants';
import * as Crypto from 'expo-crypto';
import * as WebBrowser from 'expo-web-browser';

import { Buffer } from 'buffer';

import { SecureStorage } from './storage';

// Get the app scheme from expo config
const APP_SCHEME = Constants.expoConfig?.scheme || 'ogiapp';

// Constants from your Kinde dashboard
const KINDE_AUTH_DOMAIN = Constants.expoConfig?.extra?.kindeDomain || '';
const KINDE_CLIENT_ID = Constants.expoConfig?.extra?.kindeClientId || '';

// Define redirect URIs using app scheme
const REDIRECT_URI = `${APP_SCHEME}://callback`;
const LOGOUT_REDIRECT_URI = `${APP_SCHEME}://callback`;

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'og_kinde_access_token',
  ID_TOKEN: 'og_kinde_id_token',
  REFRESH_TOKEN: 'og_kinde_refresh_token',
  CODE_VERIFIER: 'og_kinde_code_verifier',
  STATE: 'og_kinde_state',
} as const;

// Add token expiry management
interface TokenData {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  expires_at?: number; // We'll calculate this
}

class KindeAuthClient {
  private static instance: KindeAuthClient;
  private refreshPromise: Promise<TokenData> | null = null;

  private constructor() {
    if (!KINDE_AUTH_DOMAIN) throw new Error('Kinde Auth Domain is not configured');
    if (!KINDE_CLIENT_ID) throw new Error('Kinde Client ID is not configured');

    // Log configuration on initialization
    console.log('Auth Configuration:', {
      domain: KINDE_AUTH_DOMAIN,
      redirectUri: REDIRECT_URI,
      scheme: APP_SCHEME,
    });
  }

  public static getInstance(): KindeAuthClient {
    if (!KindeAuthClient.instance) {
      KindeAuthClient.instance = new KindeAuthClient();
    }
    return KindeAuthClient.instance;
  }

  // Generate a random string for state parameter
  private async generateState(): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    return Buffer.from(randomBytes).toString('hex');
  }

  // Generate code verifier for PKCE
  private async generateCodeVerifier(): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    return Buffer.from(randomBytes)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  // Generate code challenge from verifier
  private async generateCodeChallenge(verifier: string): Promise<string> {
    const hash = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, verifier);
    return Buffer.from(hash, 'hex')
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private async buildAuthUrl(action: 'login' | 'register' | 'create-org'): Promise<URL> {
    // Generate and store state and code verifier
    const state = await this.generateState();
    const codeVerifier = await this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);

    // Store state and code verifier for later verification
    await SecureStorage.setItem(STORAGE_KEYS.STATE, state);
    await SecureStorage.setItem(STORAGE_KEYS.CODE_VERIFIER, codeVerifier);

    const url = new URL(`https://${KINDE_AUTH_DOMAIN}/oauth2/auth`);
    const params = {
      client_id: KINDE_CLIENT_ID,
      redirect_uri: REDIRECT_URI,
      response_type: 'code',
      scope: 'openid profile email offline',
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      ...(action === 'register' && { prompt: 'create' }),
      ...(action === 'create-org' && { is_create_org: 'true' }),
    };

    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    console.log(`Building ${action} URL:`, url.toString());
    return url;
  }

  private async handleAuthResponse(url: string) {
    console.log('Handling auth response:', url);

    try {
      const parsedUrl = new URL(url);
      const code = parsedUrl.searchParams.get('code');
      const returnedState = parsedUrl.searchParams.get('state');
      const error = parsedUrl.searchParams.get('error');
      const errorDescription = parsedUrl.searchParams.get('error_description');

      console.log('Auth response params:', {
        code: code ? `${code.slice(0, 10)}...` : null, // Log partial code for debugging
        state: returnedState,
        error,
        errorDescription,
      });

      if (error) {
        throw new Error(`Auth error: ${error} - ${errorDescription}`);
      }

      if (!code) {
        throw new Error('No authorization code received');
      }

      // Verify state
      const storedState = await SecureStorage.getItem(STORAGE_KEYS.STATE);
      console.log('State verification:', { returned: returnedState, stored: storedState });

      if (returnedState !== storedState) {
        throw new Error('State mismatch - possible CSRF attack');
      }

      // Get stored code verifier
      const codeVerifier = await SecureStorage.getItem(STORAGE_KEYS.CODE_VERIFIER);
      if (!codeVerifier) {
        throw new Error('No code verifier found');
      }

      // Exchange code for tokens
      console.log('Exchanging code for tokens...');
      const tokenResponse = await fetch(`https://${KINDE_AUTH_DOMAIN}/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          client_id: KINDE_CLIENT_ID,
          code,
          redirect_uri: REDIRECT_URI,
          code_verifier: codeVerifier,
        }).toString(),
      });

      console.log('Token response status:', tokenResponse.status);

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.log('Token exchange failed:', errorText);
        throw new Error(`Failed to exchange code for tokens: ${errorText}`);
      }

      const tokens = await tokenResponse.json();
      console.log('Token exchange successful', {
        hasAccessToken: !!tokens.access_token,
        hasIdToken: !!tokens.id_token,
        hasRefreshToken: !!tokens.refresh_token,
        expiresIn: tokens.expires_in,
        tokenType: tokens.token_type,
      });

      await this.storeTokens(tokens);

      // Clear auth codes
      await SecureStorage.removeItem(STORAGE_KEYS.STATE);
      await SecureStorage.removeItem(STORAGE_KEYS.CODE_VERIFIER);

      return tokens;
    } catch (error) {
      console.log('Error handling auth response:', error);
      throw error;
    }
  }

  private async storeTokens(tokens: TokenData) {
    const expiresAt = Date.now() + tokens.expires_in * 1000;
    const tokensWithExpiry = { ...tokens, expires_at: expiresAt };

    await SecureStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, JSON.stringify(tokensWithExpiry));

    if (tokens.refresh_token) {
      await SecureStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh_token);
    }
  }

  private async clearAllTokens() {
    console.log('Clearing all auth tokens');
    await Promise.all([
      SecureStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN),
      SecureStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN),
      SecureStorage.removeItem(STORAGE_KEYS.ID_TOKEN),
      SecureStorage.removeItem(STORAGE_KEYS.STATE),
      SecureStorage.removeItem(STORAGE_KEYS.CODE_VERIFIER),
    ]);
  }

  private async getStoredTokens(): Promise<TokenData | null> {
    try {
      const tokenDataStr = await SecureStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      if (!tokenDataStr) return null;

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      const now = Date.now();

      // Check if token is expired or will expire in next 5 minutes
      if (!tokenData.expires_at || tokenData.expires_at - now < 5 * 60 * 1000) {
        // Try to refresh token
        try {
          return await this.refreshTokens();
        } catch (error) {
          console.log('Token refresh failed, clearing auth state');
          await this.clearAllTokens();
          return null;
        }
      }

      return tokenData;
    } catch (error) {
      console.log('Error getting stored tokens:', error);
      await this.clearAllTokens();
      return null;
    }
  }

  private async refreshTokens(): Promise<TokenData> {
    // If there's already a refresh in progress, return that promise
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = (async () => {
      try {
        const refreshToken = await SecureStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        console.log('Refreshing tokens');
        const response = await fetch(`https://${KINDE_AUTH_DOMAIN}/oauth2/token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: KINDE_CLIENT_ID,
            refresh_token: refreshToken,
          }).toString(),
        });

        if (!response.ok) {
          throw new Error('Failed to refresh token');
        }

        const tokens = await response.json();
        await this.storeTokens(tokens);
        return tokens;
      } finally {
        this.refreshPromise = null;
      }
    })();

    return this.refreshPromise;
  }

  async getValidAccessToken(): Promise<string | null> {
    try {
      const tokens = await this.getStoredTokens();

      if (!tokens) return null;

      // Check if token is expired or will expire in next 5 minutes
      const isExpiringSoon = (tokens.expires_at || 0) - Date.now() < 5 * 60 * 1000;

      if (isExpiringSoon) {
        // Try to refresh the token
        const newTokens = await this.refreshTokens();
        return newTokens.access_token;
      }

      return tokens.access_token;
    } catch (error) {
      console.log('Error getting valid access token:', error);
      return null;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      const tokens = await this.getStoredTokens();
      return !!tokens?.access_token;
    } catch {
      return false;
    }
  }

  async openAuthSessionAsync(authUrl: URL): Promise<WebBrowser.WebBrowserAuthSessionResult> {
    try {
      console.log('Opening auth session...', {
        authUrl: authUrl.toString(),
        redirectUri: REDIRECT_URI,
      });

      return await WebBrowser.openAuthSessionAsync(authUrl.toString(), REDIRECT_URI, {
        createTask: false,
      });
    } catch (error) {
      console.log('Auth session error:', error);
      throw error;
    }
  }

  async login() {
    console.log('Starting login flow...');
    try {
      // Clear any existing tokens first to ensure a fresh login
      await this.clearAllTokens();

      // Start auth flow
      if (Platform.OS === 'android') {
        await WebBrowser.warmUpAsync();
      }

      const authUrl = await this.buildAuthUrl('login');

      try {
        const result = await this.openAuthSessionAsync(authUrl);

        console.log('Auth session result:', result);

        if (result.type === 'success' && result.url) {
          const tokens = await this.handleAuthResponse(result.url);
          console.log('Login successful, fetching user profile');
          const profile = await this.getUserProfile();
          return { tokens, profile };
        }

        throw new Error(`Authentication ${result.type}`);
      } finally {
        if (Platform.OS === 'android') {
          await WebBrowser.coolDownAsync();
        }
      }
    } catch (error) {
      console.log('Login error:', error);
      // Clear tokens on login error
      await this.clearAllTokens();
      throw error;
    }
  }

  async register() {
    // Check if already authenticated
    const isAuthed = await this.isAuthenticated();
    if (isAuthed) {
      return null;
    }

    try {
      const authUrl = await this.buildAuthUrl('register');
      const result = await this.openAuthSessionAsync(authUrl);

      if (result.type === 'success' && result.url) {
        return await this.handleAuthResponse(result.url);
      }
      throw new Error('Auth session failed or was cancelled');
    } catch (error) {
      console.log('Registration error:', error);
      throw error;
    }
  }

  async createOrg(orgName?: string) {
    try {
      const authUrl = await this.buildAuthUrl('create-org');
      if (orgName) {
        authUrl.searchParams.append('org_name', orgName);
      }

      const result = await this.openAuthSessionAsync(authUrl);

      if (result.type === 'success' && result.url) {
        return await this.handleAuthResponse(result.url);
      }
    } catch (error) {
      console.log('Create org error:', error);
      throw error;
    }
  }

  async logout() {
    try {
      // Clear tokens first
      await this.clearAllTokens();

      // Then redirect to logout URL
      const logoutUrl = `https://${KINDE_AUTH_DOMAIN}/logout?redirect=${LOGOUT_REDIRECT_URI}`;
      await WebBrowser.openAuthSessionAsync(logoutUrl, LOGOUT_REDIRECT_URI, {
        createTask: false,
      });

      return true;
    } catch (error) {
      console.log('Logout error:', error);
      throw error;
    }
  }

  async getUserProfile() {
    try {
      const tokens = await this.getStoredTokens();
      if (!tokens?.access_token) {
        throw new Error('No valid access token');
      }

      console.log('Fetching user profile with token:', tokens.access_token.slice(0, 10) + '...');

      const response = await fetch(`https://${KINDE_AUTH_DOMAIN}/oauth2/user_profile`, {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      });

      console.log('Profile response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Profile fetch error:', errorText);

        if (response.status === 401) {
          // Token might be invalid, clear auth state
          await this.clearAllTokens();
          throw new Error('Authentication expired');
        }

        throw new Error(`Failed to fetch user profile: ${errorText}`);
      }

      const profile = await response.json();
      console.log('Profile fetch successful:', profile);

      return profile;
    } catch (error) {
      console.log('Get user profile error:', error);
      throw error;
    }
  }
}

export const kindeAuth = KindeAuthClient.getInstance();
