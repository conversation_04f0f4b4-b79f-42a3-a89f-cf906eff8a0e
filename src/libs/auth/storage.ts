import * as SecureStore from 'expo-secure-store';

// Split token storage to stay under 2048 byte limit
const CHUNK_SIZE = 1800; // Leave some buffer below 2048

interface ChunkedData {
  chunks: number;
  [key: string]: any;
}

export class SecureStorage {
  static async setItem(key: string, value: string): Promise<void> {
    // If value is small enough, store directly
    if (value.length <= CHUNK_SIZE) {
      await SecureStore.setItemAsync(key, value);
      return;
    }

    // Split large values into chunks
    const chunks = Math.ceil(value.length / CHUNK_SIZE);
    const chunkedData: ChunkedData = { chunks };

    // Store metadata
    await SecureStore.setItemAsync(key, JSON.stringify(chunkedData));

    // Store chunks
    for (let i = 0; i < chunks; i++) {
      const start = i * CHUNK_SIZE;
      const chunk = value.slice(start, start + CHUNK_SIZE);
      await SecureStore.setItemAsync(`${key}_chunk_${i}`, chunk);
    }
  }

  static async getItem(key: string): Promise<string | null> {
    const value = await SecureStore.getItemAsync(key);
    if (!value) return null;

    try {
      // Check if this is chunked data
      const chunkedData = JSON.parse(value) as ChunkedData;
      if (typeof chunkedData.chunks === 'number') {
        // Reassemble chunks
        let result = '';
        for (let i = 0; i < chunkedData.chunks; i++) {
          const chunk = await SecureStore.getItemAsync(`${key}_chunk_${i}`);
          if (chunk) result += chunk;
        }
        return result;
      }
    } catch {
      // Not chunked data, return as is
      return value;
    }

    return value;
  }

  static async removeItem(key: string): Promise<void> {
    const value = await SecureStore.getItemAsync(key);
    if (!value) return;

    try {
      // Check if this is chunked data
      const chunkedData = JSON.parse(value) as ChunkedData;
      if (typeof chunkedData.chunks === 'number') {
        // Remove all chunks
        const promises = [];
        for (let i = 0; i < chunkedData.chunks; i++) {
          promises.push(SecureStore.deleteItemAsync(`${key}_chunk_${i}`));
        }
        await Promise.all(promises);
      }
    } catch {
      // Not chunked data, just remove the key
    }

    await SecureStore.deleteItemAsync(key);
  }
}
