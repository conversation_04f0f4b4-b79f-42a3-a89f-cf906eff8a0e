import { storage } from '@/storage/mmkv';

interface LocalFirstOptions<T> {
  storageKey: string;
  fetchData: () => Promise<T>;
  // Optional fallback value if both storage and fetch fail
  fallback?: T;
  // Optional validation function for cached data
  validate?: (data: unknown) => data is T;
}

interface LocalFirstResult<T> {
  data: T | null;
  source: 'cache' | 'network' | 'fallback' | null;
  error?: Error;
}

export async function localFirst<T>({
  storageKey,
  fetchData,
  fallback,
  validate,
}: LocalFirstOptions<T>): Promise<LocalFirstResult<T>> {
  try {
    // Try getting from storage first
    const cachedData = storage.getString(storageKey);

    console.log('Raw cached data:', cachedData); // Debug log

    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        console.log('Parsed cached data:', parsed); // Debug log

        // Validate parsed data if validator provided
        if (validate && !validate(parsed)) {
          console.log('Cache validation failed for:', parsed); // Debug log
          throw new Error('Invalid cached data format');
        }

        return {
          data: parsed as T,
          source: 'cache',
        };
      } catch (e) {
        console.log('Cache parsing error:', e); // Debug log
        // Invalid JSON or validation failed - remove bad cache
        storage.delete(storageKey);
      }
    }

    // Fetch from network
    try {
      const response = await fetchData();
      console.log('Network response:', response); // Debug log

      if (!response) {
        throw new Error('Empty response from network');
      }

      // Make sure we're not double-stringifying
      const dataToStore = typeof response === 'string' ? response : JSON.stringify(response);

      // Cache the valid response
      storage.set(storageKey, dataToStore);

      // If response is already a string, parse it before returning
      const finalData = typeof response === 'string' ? JSON.parse(response) : response;

      return {
        data: finalData,
        source: 'network',
      };
    } catch (error) {
      console.log('Network fetch error:', error); // Debug log

      // Network fetch failed
      if (fallback !== undefined) {
        return {
          data: fallback,
          source: 'fallback',
          error: error as Error,
        };
      }

      return {
        data: null,
        source: null,
        error: error as Error,
      };
    }
  } catch (error) {
    console.log('Catastrophic error:', error); // Debug log
    // Catastrophic error
    return {
      data: null,
      source: null,
      error: error as Error,
    };
  }
}
