import { storage } from '@/storage/mmkv';

interface SectionSyncOptions {
  id: string;
  totalItems: number;
  storageKeyPrefix: string;
  fetchItem: (id: string) => Promise<any>;
  validate: (data: unknown) => boolean;
}

export async function syncWithAdjacent({
  id,
  totalItems,
  storageKeyPrefix,
  fetchItem,
  validate,
}: SectionSyncOptions) {
  const currentId = parseInt(id);
  const nextId = String(currentId + 1);

  console.log(`[SectionSync] Current ID: ${id}, Next ID: ${nextId}`);

  // Check next section cache
  const nextCached = storage.getString(`${storageKeyPrefix}${nextId}`);
  console.log(`[SectionSync] Next section cached: ${Boolean(nextCached)}`);

  if (nextCached && !validate(JSON.parse(nextCached))) {
    console.log('[SectionSync] Invalid cache found, clearing...');
    storage.delete(`${storageKeyPrefix}${nextId}`);
  }

  // Fetch current + next if needed
  console.log(`[SectionSync] Fetching current${!nextCached ? ' and next' : ''} section(s)`);
  const [current, next] = await Promise.all([
    fetchItem(id),
    !nextCached && currentId < totalItems ? fetchItem(nextId) : null,
  ]);

  // Cache validated next section
  if (next && validate(next)) {
    console.log('[SectionSync] Caching next section');
    storage.set(`${storageKeyPrefix}${nextId}`, JSON.stringify(next));
  }

  return current;
}
