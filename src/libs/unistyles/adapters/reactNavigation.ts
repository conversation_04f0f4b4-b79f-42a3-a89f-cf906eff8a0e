import type { Theme } from '@react-navigation/native';

import { dark, light } from '../theme';
import { fonts as fontTokens } from '../tokens/fonts';

const fonts = {
  regular: {
    fontFamily: fontTokens.primary.regular,
    fontWeight: '400' as const,
  },
  medium: {
    fontFamily: fontTokens.primary.semiBold,
    fontWeight: '600' as const,
  },
  bold: {
    fontFamily: fontTokens.primary.semiBold,
    fontWeight: '600' as const,
  },
  heavy: {
    fontFamily: fontTokens.primary.bold,
    fontWeight: '700' as const,
  },
};

export const createNavigationTheme = (isDark: boolean): Theme => {
  const theme = isDark ? dark : light;

  return {
    dark: isDark,
    colors: {
      primary: theme.colors.brand.primary.base,
      background: theme.colors.background.secondary,
      card: theme.colors.background.primary,
      text: theme.colors.text.primary,
      border: theme.colors.border,
      notification: theme.colors.semantic.error.active,
    },
    fonts,
  };
};

export const lightNavigationTheme = createNavigationTheme(false);
export const darkNavigationTheme = createNavigationTheme(true);
