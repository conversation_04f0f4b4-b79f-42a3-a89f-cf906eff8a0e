import { UnistylesRegistry } from 'react-native-unistyles';

import { dark, light } from './theme';
import { breakpoints } from './tokens/breakpoints';

type AppBreakpoints = typeof breakpoints;

interface AppTheme {
  light: typeof light;
  dark: typeof dark;
}

declare module 'react-native-unistyles' {
  export interface UnistylesBreakpoints extends AppBreakpoints {}
  export interface UnistylesThemes extends AppTheme {}
}

UnistylesRegistry.addBreakpoints(breakpoints).addThemes({ light, dark }).addConfig({
  adaptiveThemes: true,
  initialTheme: 'light',
});
