type ColorScale = {
  [key: number]: string;
};

export const createStateColors = (colorScale: ColorScale) => ({
  bg: colorScale[50], // Container/card backgrounds
  border: colorScale[100], // Borders, outlines
  disabled: `${colorScale[200]}80`, // Inactive state with opacity
  loading: colorScale[200], // Progress indicators
  active: colorScale[300], // Buttons, active elements
  pressed: colorScale[400], // Touch feedback
  text: colorScale[500], // Text on primary elements
});
