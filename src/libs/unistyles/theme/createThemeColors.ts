import { darkColors, lightColors } from '../tokens/colors';
import { createStateColors } from './createStateColors';

type ColorSet = typeof lightColors | typeof darkColors;

const COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
} as const;

export const createThemeColors = (colors: ColorSet) => ({
  /**
   * Brand colors represent your core identity
   * Used directly or as base for semantic colors
   */
  brand: {
    primary: {
      base: colors.primary[300], // Core brand color
      accent: colors.primary[200], // Supporting brand elements
      subtle: colors.primary[50], // Backgrounds, non-interactive
      muted: colors.primary[100], // Subdued brand presence
      emphasis: colors.primary[500], // Strong brand moments
      interaction: colors.primary[400], // Interactive feedback
    },
  },

  /**
   * Semantic colors for consistent meaning across the app
   * Each set includes: bg, border, disabled, loading, active, pressed, text
   */
  semantic: {
    primary: createStateColors(colors.primary), // Main UI interactions
    success: createStateColors(colors.mint), // Positive outcomes, completion
    warning: createStateColors(colors.orange), // Alerts, caution required
    error: createStateColors(colors.ruby), // Critical issues, blockers
    action: createStateColors(colors.orange), // Call-to-actions, key flows
    highlight: createStateColors(colors.teal), // Special attention, featured items
  },

  background: {
    primary: colors === lightColors ? COLORS.WHITE : colors.neutral[950],
    secondary: colors === lightColors ? colors.neutral[50] : colors.neutral[900],
  },

  // Surface colors for backward compatibility
  surface: {
    primary: colors === lightColors ? COLORS.WHITE : colors.neutral[950],
    secondary: colors === lightColors ? colors.neutral[50] : colors.neutral[900],
  },

  text: {
    primary: colors === lightColors ? COLORS.BLACK : colors.neutral[100], // Main text, headers
    subdued: colors === lightColors ? colors.neutral[700] : colors.neutral[300], // Labels, section headers
    secondary: colors === lightColors ? colors.neutral[600] : colors.neutral[400], // Labels, section headers
    tertiary: colors === lightColors ? colors.neutral[500] : colors.neutral[500], // Captions, helper text
    disabled: colors === lightColors ? colors.neutral[400] : colors.neutral[600], // Disabled text states
    inverse: colors === lightColors ? colors.neutral[50] : colors.neutral[950], // Text on dark backgrounds
  },

  border: colors === lightColors ? colors.neutral[100] : colors.neutral[800],

  shadow: colors === lightColors ? `${COLORS.BLACK}08` : `${COLORS.WHITE}08`,

  opacity: 0.7,
});
