/**
 * Base neutral colors used across both themes
 * Used primarily for backgrounds, borders, and text
 */
const neutralColors = {
  50: '#F2F2F7', // Primary background, cards in light mode
  100: '#E5E5EA', // Borders, dividers in light mode
  200: '#D1D1D6', // Disabled text, placeholders in light mode
  300: '#C7C7CC', // Secondary text in light mode
  400: '#AEAEB2', // Primary text in light mode
  500: '#8E8E93', // Primary text in dark mode
  600: '#636366', // Secondary text in dark mode
  700: '#48484a', // Disabled text, placeholders in dark mode
  800: '#3a3a3c', // Borders, dividers in dark mode
  900: '#2c2c2e', // Cards in dark mode
  950: '#1c1c1e', // Primary background in dark mode
};

/**
 * Light theme colors
 * Follow 60-30-10 rule:
 * 60% - Neutral colors for backgrounds
 * 30% - Primary colors for main UI elements
 * 10% - Accent colors for emphasis/calls-to-action
 */
export const lightColors = {
  neutral: {
    ...neutralColors,
  },
  // Primary blue
  primary: {
    50: '#EEF3F9', // Very light blue
    100: '#D4E2F2', // Light blue
    200: '#9EBDE0', // Medium light blue
    300: '#3967A7', // Base blue
    400: '#2C5085', // Dark blue
    500: '#1F3964', // Very dark blue
  },

  // Orange/warning
  orange: {
    50: '#FEF3EA', // Very light orange
    100: '#FDE3CC', // Light orange
    200: '#F9B587', // Medium light orange
    300: '#EC7522', // Base orange
    400: '#D15E0F', // Dark orange
    500: '#A3490C', // Very dark orange
  },

  // Teal
  teal: {
    50: '#EBF9FE', // Very light teal
    100: '#D0F1FD', // Light teal
    200: '#8FE0F9', // Medium light teal
    300: '#3BC6F3', // Base teal
    400: '#2E9EC2', // Dark teal
    500: '#217591', // Very dark teal
  },

  // Mint
  mint: {
    50: '#ECF9F8', // Very light mint
    100: '#D3F1EF', // Light mint
    200: '#8FDDDA', // Medium light mint
    300: '#3EBEB5', // Base mint
    400: '#319891', // Dark mint
    500: '#24726D', // Very dark mint
  },

  // Ruby
  ruby: {
    50: '#FCE9EB', // Very light ruby
    100: '#F9CCD1', // Light ruby
    200: '#F08991', // Medium light ruby
    300: '#E33245', // Base ruby
    400: '#B62837', // Dark ruby
    500: '#891E29', // Very dark ruby
  },
} as const;

/**
 * Dark theme colors
 * Follows same 60-30-10 pattern but with inverted lightness
 */
export const darkColors = {
  neutral: {
    ...neutralColors,
  },
  // Primary blue
  primary: {
    50: '#1A2537', // Darkest shade - decreased brightness by ~90%
    100: '#243552', // Decreased brightness by ~80%
    200: '#2D4369', // Decreased brightness by ~60%
    300: '#4276C0', // Base color
    400: '#6390D8', // Increased brightness by ~20%
    500: '#84A9F0', // Increased brightness by ~40%
  },

  // Orange/warning
  orange: {
    50: '#351C0B',
    100: '#4A2810',
    200: '#783F19',
    300: '#FF8727', // Base color
    400: '#FFA152',
    500: '#FFBB7D',
  },

  // Teal
  teal: {
    50: '#0A2C33',
    100: '#134D59',
    200: '#1C7080',
    300: '#44E4FF', // Base color
    400: '#6EEAFF',
    500: '#98EFFF',
  },

  // Mint
  mint: {
    50: '#0B2826',
    100: '#134745',
    200: '#1C6964',
    300: '#47DAD0', // Base color
    400: '#6EE3DB',
    500: '#95ECE6',
  },

  // Ruby
  ruby: {
    50: '#2A0D11',
    100: '#471218',
    200: '#751B23',
    300: '#FF394F', // Base color
    400: '#FF6375',
    500: '#FF8D9A',
  },
} as const;
