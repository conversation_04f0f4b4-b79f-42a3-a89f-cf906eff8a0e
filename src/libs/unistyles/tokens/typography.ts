import { fonts } from './fonts';
import { lineHeights } from './line_heights';

export const typography = {
  // Size scale for backward compatibility
  size: {
    xs: 11,
    sm: 13,
    md: 17,
    lg: 20,
    xl: 28,
    xxl: 34,
  },
  // Line height scale for backward compatibility
  lineHeight: lineHeights,
  // Typography styles
  h1: {
    fontFamily: fonts.primary.bold,
    fontSize: 34, // Maps to iOS largeTitle / close to Material displaySmall
    lineHeight: lineHeights.huge,
    fontWeight: '700',
  },
  h2: {
    fontFamily: fonts.primary.semiBold,
    fontSize: 28, // Maps to iOS title1 / Material headlineMedium
    lineHeight: lineHeights.detached,
    fontWeight: '600',
  },
  h3: {
    fontFamily: fonts.primary.semiBold,
    fontSize: 22, // Maps to iOS title2 / Material titleLarge
    lineHeight: lineHeights.relaxed,
    fontWeight: '600',
  },
  h4: {
    fontFamily: fonts.primary.semiBold,
    fontSize: 20, // Maps to iOS title3
    lineHeight: lineHeights.relaxed,
    fontWeight: '600',
  },
  body1: {
    fontFamily: fonts.primary.regular,
    fontSize: 17, // Maps to iOS body
    lineHeight: lineHeights.normal,
    fontWeight: '400',
  },
  body2: {
    fontFamily: fonts.primary.regular,
    fontSize: 15, // Maps to iOS subheadline
    lineHeight: lineHeights.normal,
    fontWeight: '400',
  },
  subtitle1: {
    fontFamily: fonts.primary.regular,
    fontSize: 13, // Maps to iOS footnote
    lineHeight: lineHeights.tight,
    fontWeight: '400',
  },
  subtitle2: {
    fontFamily: fonts.primary.regular,
    fontSize: 12, // Maps to iOS caption1
    lineHeight: lineHeights.none,
    fontWeight: '400',
  },
  caption: {
    fontFamily: fonts.primary.regular,
    fontSize: 11, // Maps to iOS caption2
    lineHeight: lineHeights.none,
    fontWeight: '400',
  },
  label: {
    fontFamily: fonts.primary.semiBold,
    fontSize: 11,
    lineHeight: lineHeights.none,
    fontWeight: '600',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
} as const;
