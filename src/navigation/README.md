# Navigation Structure

This directory contains the navigation configuration and components for the OperationGo app.

## Overview

The navigation system in this app is based on Expo Router, which uses a file-based routing approach similar to Next.js. The `navigation` directory provides additional abstractions and configurations on top of Expo Router to make navigation more maintainable and feature-oriented.

## Directory Structure

- `index.ts` - Main entry point that exports all navigation components and utilities
- `routes.ts` - Defines app routes and route-related utilities
- `types.ts` - TypeScript interfaces for navigation
- `TabNavigation.tsx` - Reusable tab navigation component

## Usage

### Tab Routes

Tab routes are defined in `routes.ts`:

```typescript
export const tabRoutes = [
  { name: 'index', href: '/', label: 'Home', routeName: 'index' },
  { name: 'gospel', href: '/gospel', label: 'Gospel', routeName: 'gospel' },
  // ...
];
```

These routes are used in `(tabs)/_layout.tsx` to generate the main tab navigation:

```typescript
import { tabRoutes } from '@/navigation/routes';

export default function TabLayout() {
  return (
    <UITabsLayout routes={tabRoutes} />
  );
}
```

### Navigation Helpers

The `routes.ts` file also includes helper functions:

- `getRouteByName(name)` - Find a route by its name
- `getRouteIndex(name)` - Get the index of a route by name

### Custom Navigation Components

For custom navigation needs, the `TabNavigation.tsx` component provides a styled tab navigation implementation that can be used throughout the app.

## Feature Navigation

Each feature can define its own navigation structure within the feature directory:

```
/features/gospel/
  /navigation/
    routes.ts      - Gospel-specific routes
    helpers.ts     - Navigation helpers for Gospel feature
```

Feature-specific navigation should be imported and used within the feature's screens.

## Best Practices

1. Keep all app-wide routes defined in `routes.ts`
2. Use type-safe navigation with the types defined in `types.ts`
3. For deep linking, register routes properly in `routes.ts`
4. When adding a new feature, update the tab routes if needed