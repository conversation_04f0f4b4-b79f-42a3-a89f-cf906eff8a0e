import React from 'react';

import { TouchableOpacity, View } from 'react-native';

import { Link, usePathname } from 'expo-router';

import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { icons } from '@/components/ui/TabIcons';
import { ThemedText } from '@/components/ui/ThemedText';

import { tabRoutes } from './routes';
import { TabRoute } from './types';

interface TabNavigationProps {
  routes?: TabRoute[];
}

export function TabNavigation({ routes = tabRoutes }: TabNavigationProps) {
  const { styles } = useStyles(stylesheet);
  const pathname = usePathname();

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        {routes.map((route) => {
          const isActive =
            pathname === String(route.href) ||
            (pathname.startsWith(String(route.href)) && String(route.href) !== '/');
          const IconComponent = icons[route.name];

          return (
            <Link key={route.name} href={route.href} asChild>
              <TouchableOpacity
                style={[styles.tabButton, isActive && styles.activeTab]}
                activeOpacity={0.7}
              >
                {IconComponent && (
                  <IconComponent
                    size={24}
                    color={isActive ? styles.activeTabLabel.color : styles.inactiveTabLabel.color}
                  />
                )}
                <ThemedText
                  style={[
                    styles.tabLabel,
                    isActive ? styles.activeTabLabel : styles.inactiveTabLabel,
                  ]}
                >
                  {route.label}
                </ThemedText>
              </TouchableOpacity>
            </Link>
          );
        })}
      </View>
    </View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingBottom: theme.spacing.sm,
    paddingTop: theme.spacing.xs,
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.xs,
    borderRadius: theme.radius.md,
    flex: 1,
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabLabel: {
    fontSize: theme.typography.subtitle2.fontSize,
    marginTop: 2,
  },
  activeTabLabel: {
    color: theme.colors.brand.primary.base,
  },
  inactiveTabLabel: {
    color: theme.colors.text.secondary,
  },
}));
