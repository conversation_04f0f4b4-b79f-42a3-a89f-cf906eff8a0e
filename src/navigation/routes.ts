import { TabRoute } from './types';

/**
 * Main tab routes for the application.
 * These define the primary navigation structure of the app.
 */
export const tabRoutes: TabRoute[] = [
  {
    name: 'home',
    label: 'Home',
    href: '/',
    routeName: 'home',
  },
  {
    name: 'gospel',
    label: 'Gospel',
    href: '/gospel',
    routeName: 'gospel',
  },
  {
    name: 'devotional',
    label: 'Devotional',
    href: '/devotional',
    routeName: 'devotional',
  },
  {
    name: 'lessons',
    label: 'Lessons',
    href: '/lessons',
    routeName: 'lessons',
  },
  {
    name: 'support',
    label: 'Support',
    href: '/support',
    routeName: 'support',
  },
];
