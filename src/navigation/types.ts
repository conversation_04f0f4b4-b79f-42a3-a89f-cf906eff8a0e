import { Href } from 'expo-router';

/**
 * Basic interface for a tab route in the application
 */
export interface TabRoute {
  /**
   * Unique name/identifier for the tab
   */
  name: string;

  /**
   * Display label shown to users
   */
  label: string;

  /**
   * Route href used for navigation
   */
  href: Href;

  /**
   * Route name used for icon mapping
   */
  routeName: string;
}

/**
 * Navigation parameters for screens that require IDs or other data
 */
export interface NavigationParams {
  id?: string;
  [key: string]: string | undefined;
}

/**
 * Screen configuration for navigation components
 */
export interface ScreenConfig {
  title: string;
  showBackButton?: boolean;
  hideTabBar?: boolean;
}
