import { atom } from 'jotai';

import { SecureStorage } from '@/libs/auth/storage';
import { atomWithMMKV } from '@/storage/mmkv';

const USER_PROFILE_KEY = 'og_user_profile';

// Types
export interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  given_name?: string;
  family_name?: string;
  email?: string;
  preferred_email?: string;
  picture?: string;
}

// Base atoms with default values
export const isAuthenticatedAtom = atomWithMMKV<boolean>('auth-authenticated', false);
export const isLoadingAtom = atom<boolean>(false);
export const authErrorAtom = atom<string | null>(null);

// Base atom for storing the profile
const baseProfileAtom = atom<UserProfile | null>(null);

// Derived atom that handles persistence
export const userProfileAtom = atom(
  // Read
  async (get) => {
    const isAuthenticated = get(isAuthenticatedAtom);
    const baseProfile = get(baseProfileAtom);

    if (!isAuthenticated) return null;

    if (baseProfile) {
      return baseProfile;
    }

    try {
      const profileStr = await SecureStorage.getItem(USER_PROFILE_KEY);
      return profileStr ? (JSON.parse(profileStr) as UserProfile) : null;
    } catch (error) {
      console.log('Error reading user profile:', error);
      return null;
    }
  },
  // Write
  async (_get, set, newProfile: UserProfile | null) => {
    try {
      if (newProfile) {
        await SecureStorage.setItem(USER_PROFILE_KEY, JSON.stringify(newProfile));
        set(baseProfileAtom, newProfile);
      } else {
        await SecureStorage.removeItem(USER_PROFILE_KEY);
        set(baseProfileAtom, newProfile);
      }
    } catch (error) {
      console.log('Error storing user profile:', error);
    }
  },
);

// Combined auth state
export const authStateAtom = atom(async (get) => {
  const isAuthenticated = get(isAuthenticatedAtom);
  const userProfile = await get(userProfileAtom);
  const isLoading = get(isLoadingAtom);
  const error = get(authErrorAtom);

  return {
    isLoading,
    isAuthenticated,
    userProfile,
    error,
  };
});
