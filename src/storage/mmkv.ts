import { MMKV } from 'react-native-mmkv';

import { atomWithStorage, createJSONStorage } from 'jotai/utils';

export const storage = new MMKV();

const getItem = (key: string): string | null => {
  const value = storage.getString(key);
  return value ?? null;
};

const setItem = (key: string, value: string): void => {
  storage.set(key, value);
};

const removeItem = (key: string): void => {
  storage.delete(key);
};

const clearAll = (): void => {
  storage.clearAll();
};

export const atomWithMMKV = <T>(key: string, initialValue: T) =>
  atomWithStorage<T>(
    key,
    initialValue,
    createJSONStorage<T>(() => ({
      getItem,
      setItem,
      removeItem,
      clearAll,
    })),
  );
